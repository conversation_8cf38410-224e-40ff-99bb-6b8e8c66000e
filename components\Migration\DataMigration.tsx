import React, { useState } from 'react';
import { migrationApi } from '../../services/apiService';
import { getEvents } from '../../services/eventService';
import { getEventTypes } from '../../services/eventTypeService';
import { getShiftTypes } from '../../services/shiftTypeService';
import { getTheoreticalCalendar } from '../../services/theoreticalCalendarService';

interface MigrationResult {
  events: { created: number; skipped: number; errors: any[] };
  eventTypes: { created: number; updated: number; skipped: number; errors: any[] };
  shiftTypes: { created: number; updated: number; skipped: number; errors: any[] };
  theoreticalCalendar: { created: number; updated: number; errors: any[] };
}

interface DataMigrationProps {
  onMigrationComplete: () => void;
}

export const DataMigration: React.FC<DataMigrationProps> = ({ onMigrationComplete }) => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<MigrationResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const migrateData = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // Get data from localStorage
      const events = await getEvents();
      const eventTypes = await getEventTypes();
      const shiftTypes = await getShiftTypes();
      
      // Get theoretical calendars for current and next year
      const currentYear = new Date().getFullYear();
      const theoreticalCalendars: Record<string, Record<string, string>> = {};
      
      for (const year of [currentYear - 1, currentYear, currentYear + 1]) {
        const calendar = await getTheoreticalCalendar(year);
        if (Object.keys(calendar).length > 0) {
          theoreticalCalendars[year.toString()] = calendar;
        }
      }

      // Migrate data to database
      const migrationResponse = await migrationApi.fromLocalStorage({
        events,
        eventTypes,
        shiftTypes,
        theoreticalCalendars
      });

      setResult(migrationResponse.results);
      
      if (migrationResponse.summary.totalErrors === 0) {
        setTimeout(() => {
          onMigrationComplete();
        }, 3000);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error durante la migración');
    } finally {
      setLoading(false);
    }
  };

  const getTotalItems = () => {
    if (!result) return 0;
    return (
      result.events.created + result.events.skipped +
      result.eventTypes.created + result.eventTypes.updated +
      result.shiftTypes.created + result.shiftTypes.updated +
      result.theoreticalCalendar.created + result.theoreticalCalendar.updated
    );
  };

  const getTotalErrors = () => {
    if (!result) return 0;
    return (
      result.events.errors.length +
      result.eventTypes.errors.length +
      result.shiftTypes.errors.length +
      result.theoreticalCalendar.errors.length
    );
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">
        Migración de Datos
      </h2>
      
      <div className="mb-6">
        <p className="text-gray-600 mb-4">
          Este proceso migrará todos tus datos del almacenamiento local (localStorage) 
          a la base de datos del servidor. Esto incluye:
        </p>
        <ul className="list-disc list-inside text-gray-600 space-y-1">
          <li>Eventos registrados</li>
          <li>Tipos de eventos configurados</li>
          <li>Tipos de turnos</li>
          <li>Calendarios teóricos</li>
        </ul>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
          <h3 className="text-sm font-medium text-red-800">Error de migración:</h3>
          <p className="text-sm text-red-700 mt-1">{error}</p>
        </div>
      )}

      {result && (
        <div className="mb-6 space-y-4">
          <div className="p-4 bg-green-50 border border-green-200 rounded-md">
            <h3 className="text-sm font-medium text-green-800 mb-2">
              Migración completada
            </h3>
            <div className="text-sm text-green-700">
              <p><strong>Total de elementos migrados:</strong> {getTotalItems()}</p>
              <p><strong>Errores:</strong> {getTotalErrors()}</p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="p-3 bg-blue-50 border border-blue-200 rounded">
              <h4 className="font-medium text-blue-800">Eventos</h4>
              <p className="text-sm text-blue-700">
                Creados: {result.events.created}, Omitidos: {result.events.skipped}
              </p>
              {result.events.errors.length > 0 && (
                <p className="text-sm text-red-600">Errores: {result.events.errors.length}</p>
              )}
            </div>

            <div className="p-3 bg-purple-50 border border-purple-200 rounded">
              <h4 className="font-medium text-purple-800">Tipos de Eventos</h4>
              <p className="text-sm text-purple-700">
                Creados: {result.eventTypes.created}, Actualizados: {result.eventTypes.updated}
              </p>
              {result.eventTypes.errors.length > 0 && (
                <p className="text-sm text-red-600">Errores: {result.eventTypes.errors.length}</p>
              )}
            </div>

            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded">
              <h4 className="font-medium text-yellow-800">Tipos de Turnos</h4>
              <p className="text-sm text-yellow-700">
                Creados: {result.shiftTypes.created}, Actualizados: {result.shiftTypes.updated}
              </p>
              {result.shiftTypes.errors.length > 0 && (
                <p className="text-sm text-red-600">Errores: {result.shiftTypes.errors.length}</p>
              )}
            </div>

            <div className="p-3 bg-indigo-50 border border-indigo-200 rounded">
              <h4 className="font-medium text-indigo-800">Calendario Teórico</h4>
              <p className="text-sm text-indigo-700">
                Creados: {result.theoreticalCalendar.created}, Actualizados: {result.theoreticalCalendar.updated}
              </p>
              {result.theoreticalCalendar.errors.length > 0 && (
                <p className="text-sm text-red-600">Errores: {result.theoreticalCalendar.errors.length}</p>
              )}
            </div>
          </div>

          {getTotalErrors() === 0 && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-md">
              <p className="text-sm text-green-700">
                ✅ Migración exitosa. Serás redirigido automáticamente en unos segundos...
              </p>
            </div>
          )}
        </div>
      )}

      <div className="flex justify-center">
        <button
          onClick={migrateData}
          disabled={loading}
          className="px-6 py-3 bg-indigo-600 text-white font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <span className="flex items-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Migrando datos...
            </span>
          ) : (
            'Iniciar Migración'
          )}
        </button>
      </div>
    </div>
  );
};
