
import React, { useState, useMemo } from 'react';
import { Event, EventTypeConfigItem, ShiftTypeConfigItem, ComputationType, calculateEventNetHours, EventCategory } from '../types';
import { MONTH_NAMES } from '../constants';
import * as theoreticalCalendarService from '../services/theoreticalCalendarService';
import Pie<PERSON>hart from './charts/PieChart';
import Bar<PERSON>hart from './charts/BarChart';
import LineC<PERSON> from './charts/LineChart';
import DonutChart from './charts/DonutChart';
import HeatmapChart from './charts/HeatmapChart';

interface StatsDashboardProps {
  isOpen: boolean;
  onClose: () => void;
  events: Event[];
  initialYear: number;
  onShowAdvancedStats: () => void;
  eventTypes: EventTypeConfigItem[];
  shiftTypes: ShiftTypeConfigItem[];
  eventTypesMap: Record<string, EventTypeConfigItem>;
}

const getTodayString = () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return new Date(today.getTime() - today.getTimezoneOffset() * 60000).toISOString().split('T')[0];
};

const StatsDashboard: React.FC<StatsDashboardProps> = ({ isOpen, onClose, events, initialYear, onShowAdvancedStats, eventTypes, shiftTypes, eventTypesMap }) => {
  const [selectedYear, setSelectedYear] = useState(initialYear);

  const shiftTypesMap = useMemo(() => Object.fromEntries(shiftTypes.map(item => [item.id, item])), [shiftTypes]);

  const availableYears = useMemo(() => {
    const years = new Set(events.map(e => new Date(e.date).getUTCFullYear()));
    years.add(initialYear);
    return Array.from(years).sort((a, b) => b - a);
  }, [events, initialYear]);

  const todayString = getTodayString();

  const yearData = useMemo(() => {
    const theoreticalCalendar = theoreticalCalendarService.getTheoreticalCalendar(selectedYear);
    const yearEvents = events.filter(e => e.date.startsWith(String(selectedYear)));

    // Data up to today (inclusive) for real-time KPIs
    const eventsUpToToday = yearEvents.filter(e => e.date <= todayString);

    // --- Hour Calculations ---
    const computedHoursUpToToday = eventsUpToToday.reduce((acc, event) => {
      const eventType = eventTypesMap[event.typeId];
      return acc + calculateEventNetHours(event, eventType);
    }, 0);

    let theoreticalHoursUpToToday = 0;
    let theoreticalHoursFuture = 0;

    for (const dateStr in theoreticalCalendar) {
      const shift = shiftTypesMap[theoreticalCalendar[dateStr]];
      if (shift) {
        if (dateStr <= todayString) {
          theoreticalHoursUpToToday += shift.hours;
        } else {
          theoreticalHoursFuture += shift.hours;
        }
      }
    }

    const totalTheoreticalHoursYear = theoreticalHoursUpToToday + theoreticalHoursFuture;
    const projectedComputedHoursYear = computedHoursUpToToday + theoreticalHoursFuture; // Approximation

    // --- KPI Calculations ---
    const balanceUpToToday = computedHoursUpToToday - theoreticalHoursUpToToday;
    const hourBank = Math.max(0, balanceUpToToday);
    const projectedAnnualBalance = projectedComputedHoursYear - totalTheoreticalHoursYear;

    const vacationsTaken = eventsUpToToday.filter(e => eventTypesMap[e.typeId]?.category === EventCategory.Vacaciones).length;
    const futurePlannedVacations = yearEvents.filter(e => e.date > todayString && eventTypesMap[e.typeId]?.category === EventCategory.Vacaciones).length;

    const annualPace = totalTheoreticalHoursYear > 0 ? (computedHoursUpToToday / totalTheoreticalHoursYear) * 100 : 0;

    // --- Chart Data ---
    const categoryDistributionData = eventsUpToToday.reduce((acc, event) => {
      const category = eventTypesMap[event.typeId]?.category || 'Sin Categoría';
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Distribución de horas por categoría
    const categoryHoursData = eventsUpToToday.reduce((acc, event) => {
      const category = eventTypesMap[event.typeId]?.category || 'Sin Categoría';
      const hours = calculateEventNetHours(event, eventTypesMap[event.typeId]);
      acc[category] = (acc[category] || 0) + Math.abs(hours); // Usar valor absoluto para mostrar tiempo dedicado
      return acc;
    }, {} as Record<string, number>);

    // Análisis por tipos de eventos específicos
    const eventTypeAnalysis = eventsUpToToday.reduce((acc, event) => {
      const eventType = eventTypesMap[event.typeId];
      if (!eventType) return acc;

      if (!acc[eventType.id]) {
        acc[eventType.id] = {
          label: eventType.label,
          category: eventType.category,
          count: 0,
          totalHours: 0,
          color: eventType.color
        };
      }

      acc[eventType.id].count += 1;
      acc[eventType.id].totalHours += calculateEventNetHours(event, eventType);
      return acc;
    }, {} as Record<string, { label: string; category: EventCategory; count: number; totalHours: number; color: string }>);

    const monthlyBalanceData = Array(12).fill(null).map((_, monthIndex) => {
      const currentMonthStr = `${selectedYear}-${String(monthIndex + 1).padStart(2, '0')}`;
      let theoretical = 0;
      let worked = 0;

      for (const dateStr in theoreticalCalendar) {
        if (dateStr.startsWith(currentMonthStr)) {
          const shift = shiftTypesMap[theoreticalCalendar[dateStr]];
          if (shift) theoretical += shift.hours;
        }
      }

      for (const event of yearEvents) {
        if (event.date.startsWith(currentMonthStr)) {
          const eventType = eventTypesMap[event.typeId];
          worked += calculateEventNetHours(event, eventType);
        }
      }
      return { theoretical, worked, balance: worked - theoretical };
    });

    // Tendencias mensuales por categoría
    const monthlyTrendsByCategory = Array(12).fill(null).map((_, monthIndex) => {
      const currentMonthStr = `${selectedYear}-${String(monthIndex + 1).padStart(2, '0')}`;
      const monthEvents = yearEvents.filter(e => e.date.startsWith(currentMonthStr));

      const categoryData: Record<string, number> = {};
      Object.values(EventCategory).forEach(category => {
        categoryData[category] = 0;
      });

      monthEvents.forEach(event => {
        const category = eventTypesMap[event.typeId]?.category || 'Sin Categoría';
        categoryData[category] += 1;
      });

      return {
        month: MONTH_NAMES[monthIndex],
        ...categoryData
      };
    });

    // Mapa de calor: días de la semana vs tipos de eventos
    const weekdayEventHeatmap = (() => {
      const heatmapData: Array<{ x: string; y: string; value: number; displayValue?: string }> = [];
      const weekdays = ['Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado', 'Domingo'];
      const topEventTypes = Object.values(eventTypeAnalysis)
        .sort((a, b) => b.count - a.count)
        .slice(0, 6); // Top 6 tipos de eventos

      weekdays.forEach(weekday => {
        topEventTypes.forEach(eventType => {
          const weekdayIndex = weekdays.indexOf(weekday);
          const eventsOnWeekday = eventsUpToToday.filter(event => {
            const eventDate = new Date(event.date + 'T00:00:00Z');
            const dayOfWeek = (eventDate.getUTCDay() + 6) % 7; // Convertir domingo=0 a lunes=0
            // Buscar el ID del tipo de evento que coincida con el label
            const eventTypeId = Object.keys(eventTypeAnalysis).find(id =>
              eventTypeAnalysis[id].label === eventType.label
            );
            return dayOfWeek === weekdayIndex && event.typeId === eventTypeId;
          });

          heatmapData.push({
            x: weekday,
            y: eventType.label,
            value: eventsOnWeekday.length,
            displayValue: eventsOnWeekday.length > 0 ? eventsOnWeekday.length.toString() : ''
          });
        });
      });

      return {
        data: heatmapData,
        xLabels: weekdays,
        yLabels: topEventTypes.map(et => et.label)
      };
    })();

    return {
      yearEvents,
      eventsUpToToday,
      kpis: {
        balanceUpToToday,
        hourBank,
        projectedAnnualBalance,
        vacationsTaken,
        futurePlannedVacations,
        annualPace,
        computedHoursUpToToday,
        theoreticalHoursUpToToday
      },
      charts: {
        categoryDistributionData,
        categoryHoursData,
        monthlyBalanceData,
        monthlyTrendsByCategory,
        weekdayEventHeatmap,
        eventTypeAnalysis,
      }
    };
  }, [selectedYear, events, todayString, eventTypesMap, shiftTypesMap]);

  if (!isOpen) return null;

  const colorPalette = ['blue-500', 'green-500', 'purple-500', 'yellow-500', 'red-500', 'pink-500', 'indigo-500'];
  const stringToColor = (str: string) => {
    let hash = 0;
    for (let i = 0; i < str.length; i++) { hash = str.charCodeAt(i) + ((hash << 5) - hash); }
    return colorPalette[Math.abs(hash) % colorPalette.length];
  };

  const pieChartData = Object.entries(yearData.charts.categoryDistributionData).map(([category, value]) => ({
    label: category,
    value,
    color: stringToColor(category),
  }));

  // Datos para el gráfico de dona de horas por categoría
  const donutChartData = Object.entries(yearData.charts.categoryHoursData).map(([category, hours]) => ({
    label: category,
    value: Object.entries(yearData.charts.categoryDistributionData).find(([cat]) => cat === category)?.[1] || 0,
    hours: hours,
    color: stringToColor(category),
  }));

  // Datos para el análisis detallado por tipos de eventos
  const eventTypeDonutData = Object.values(yearData.charts.eventTypeAnalysis)
    .sort((a, b) => b.count - a.count)
    .slice(0, 8) // Top 8 tipos de eventos
    .map(eventType => ({
      label: eventType.label,
      value: eventType.count,
      hours: Math.abs(eventType.totalHours),
      color: eventType.color,
    }));

  const barChartData = {
    labels: MONTH_NAMES,
    datasets: [
      { label: 'Horas Teóricas', data: yearData.charts.monthlyBalanceData.map(m => m.theoretical), color: 'bg-gray-300' },
      { label: 'Horas Computadas', data: yearData.charts.monthlyBalanceData.map(m => m.worked), color: 'bg-indigo-500' },
    ]
  };

  // Datos para el gráfico de líneas de balance mensual
  const lineChartData = {
    labels: MONTH_NAMES,
    datasets: [
      {
        label: 'Balance Mensual',
        data: yearData.charts.monthlyBalanceData.map(m => m.balance),
        color: 'blue-500',
        strokeColor: 'blue-500'
      },
      {
        label: 'Balance Acumulado',
        data: yearData.charts.monthlyBalanceData.reduce((acc, m, index) => {
          const prevBalance = index > 0 ? acc[index - 1] : 0;
          acc.push(prevBalance + m.balance);
          return acc;
        }, [] as number[]),
        color: 'green-500',
        strokeColor: 'green-500'
      },
    ]
  };

  // Datos para el gráfico de líneas de tendencias por categoría
  const categoryTrendsData = {
    labels: MONTH_NAMES,
    datasets: Object.values(EventCategory).map((category, index) => ({
      label: category,
      data: yearData.charts.monthlyTrendsByCategory.map(month => month[category] || 0),
      color: colorPalette[index % colorPalette.length],
      strokeColor: colorPalette[index % colorPalette.length]
    }))
  };

  const KpiCard: React.FC<{ title: string; value: string; color?: string; subtext?: string }> = ({ title, value, color = 'text-gray-800', subtext }) => (
    <div className="bg-white p-5 rounded-lg shadow">
      <h4 className="text-gray-500 font-semibold truncate">{title}</h4>
      <p className={`text-3xl font-bold ${color}`}>{value}</p>
      {subtext && <p className="text-xs text-gray-400 mt-1">{subtext}</p>}
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 z-40 flex justify-center items-center p-4">
      <div className="bg-gray-100 rounded-xl shadow-2xl w-full max-w-6xl p-6 relative animate-fade-in-up flex flex-col" style={{ height: '95vh' }}>
        <div className="flex-shrink-0 flex justify-between items-center mb-6 pb-4 border-b border-gray-300">
          <h2 className="text-3xl font-bold text-gray-800">Panel de Estadísticas</h2>
          <div className="flex items-center space-x-4">
            <select
              value={selectedYear}
              onChange={(e) => setSelectedYear(Number(e.target.value))}
              className="block border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              {availableYears.map(year => <option key={year} value={year}>{year}</option>)}
            </select>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-800">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
            </button>
          </div>
        </div>

        <div className="overflow-y-auto flex-grow pr-3">
          {yearData.yearEvents.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <p className="text-xl text-gray-500">No hay datos de eventos para el año {selectedYear}.</p>
            </div>
          ) : (
            <div className="space-y-6">
              <h3 className="text-xl font-bold text-gray-700">KPIs Clave</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
                <KpiCard title="Balance a día de hoy" value={`${yearData.kpis.balanceUpToToday.toFixed(2)}h`} color={yearData.kpis.balanceUpToToday >= 0 ? 'text-green-600' : 'text-red-600'} subtext="Real vs Teórico hasta hoy" />
                <KpiCard title="Banco de Horas" value={`${yearData.kpis.hourBank.toFixed(2)}h`} color="text-blue-600" subtext="Horas extra disponibles" />
                <KpiCard title="Proyección Balance Anual" value={`${yearData.kpis.projectedAnnualBalance.toFixed(2)}h`} color={yearData.kpis.projectedAnnualBalance >= 0 ? 'text-blue-600' : 'text-orange-600'} subtext="Estimación a final de año" />
                <KpiCard title="Vacaciones Disfrutadas" value={`${yearData.kpis.vacationsTaken}d`} color="text-green-600" subtext={`+${yearData.kpis.futurePlannedVacations}d planificadas`} />
              </div>

              <h3 className="text-xl font-bold text-gray-700 mt-6">Análisis de Horas</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
                <KpiCard title="Horas Teóricas (hasta hoy)" value={`${yearData.kpis.theoreticalHoursUpToToday.toFixed(2)}h`} />
                <KpiCard title="Horas Computadas (hasta hoy)" value={`${yearData.kpis.computedHoursUpToToday.toFixed(2)}h`} />
                <KpiCard title="Ritmo Anual (Proyectado)" value={`${yearData.kpis.annualPace.toFixed(1)}%`} subtext="Porcentaje completado de la jornada teórica anual" />
              </div>

              {/* Sección de Gráficos Principales */}
              <h3 className="text-xl font-bold text-gray-700 mt-6">Análisis Visual</h3>
              <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 mt-6">
                <div className="lg:col-span-2 bg-white p-5 rounded-lg shadow">
                  <PieChart title="Distribución por Categoría" data={pieChartData} />
                </div>
                <div className="lg:col-span-3 bg-white p-5 rounded-lg shadow">
                  <BarChart title="Evolución Mensual de Horas" data={barChartData} />
                </div>
              </div>

              {/* Sección de Análisis Detallado */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                <div className="bg-white p-5 rounded-lg shadow">
                  <DonutChart
                    title="Distribución de Horas por Categoría"
                    data={donutChartData}
                    showHours={true}
                    centerText={Object.values(yearData.charts.categoryHoursData).reduce((a, b) => a + b, 0).toFixed(0)}
                    centerSubtext="horas totales"
                  />
                </div>
                <div className="bg-white p-5 rounded-lg shadow">
                  <DonutChart
                    title="Top Tipos de Eventos"
                    data={eventTypeDonutData}
                    showHours={true}
                    centerText={eventTypeDonutData.reduce((sum, item) => sum + item.value, 0).toString()}
                    centerSubtext="eventos"
                  />
                </div>
              </div>

              {/* Sección de Tendencias */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                <div className="bg-white p-5 rounded-lg shadow">
                  <LineChart
                    title="Balance Mensual y Acumulado"
                    data={lineChartData}
                    yAxisLabel="Horas"
                  />
                </div>
                <div className="bg-white p-5 rounded-lg shadow">
                  <LineChart
                    title="Tendencias por Categoría"
                    data={categoryTrendsData}
                    yAxisLabel="Eventos"
                  />
                </div>
              </div>

              {/* Mapa de Calor */}
              {yearData.charts.weekdayEventHeatmap.data.length > 0 && (
                <div className="bg-white p-5 rounded-lg shadow mt-6">
                  <HeatmapChart
                    title="Patrones de Actividad: Días de la Semana vs Tipos de Eventos"
                    data={yearData.charts.weekdayEventHeatmap.data}
                    xLabels={yearData.charts.weekdayEventHeatmap.xLabels}
                    yLabels={yearData.charts.weekdayEventHeatmap.yLabels}
                    colorScheme="blue"
                  />
                </div>
              )}

              <div className="bg-white p-6 rounded-lg shadow text-center mt-6">
                <h3 className="text-xl font-bold text-gray-700 mb-2">¿Necesitas un análisis más profundo?</h3>
                <p className="text-gray-600 mb-4">Utiliza la IA de Gemini para obtener conclusiones y recomendaciones basadas en tus datos anuales.</p>
                <button onClick={onShowAdvancedStats} className="bg-indigo-600 text-white font-bold py-2 px-6 rounded-lg hover:bg-indigo-700 transition duration-300 inline-flex items-center space-x-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" /></svg>
                  <span>Análisis Avanzado con IA</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StatsDashboard;
