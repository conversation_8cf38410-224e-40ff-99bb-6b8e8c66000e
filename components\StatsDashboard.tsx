
import React, { useState, useMemo } from 'react';
import { Event, EventTypeConfigItem, ShiftTypeConfigItem, ComputationType, calculateEventNetHours, EventCategory } from '../types';
import { MONTH_NAMES } from '../constants';
import * as theoreticalCalendarService from '../services/theoreticalCalendarService';
import Pie<PERSON>hart from './charts/PieChart';
import Bar<PERSON>hart from './charts/BarChart';

interface StatsDashboardProps {
  isOpen: boolean;
  onClose: () => void;
  events: Event[];
  initialYear: number;
  onShowAdvancedStats: () => void;
  eventTypes: EventTypeConfigItem[];
  shiftTypes: ShiftTypeConfigItem[];
  eventTypesMap: Record<string, EventTypeConfigItem>;
}

const getTodayString = () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return new Date(today.getTime() - today.getTimezoneOffset() * 60000).toISOString().split('T')[0];
};

const StatsDashboard: React.FC<StatsDashboardProps> = ({ isOpen, onClose, events, initialYear, onShowAdvancedStats, eventTypes, shiftTypes, eventTypesMap }) => {
  const [selectedYear, setSelectedYear] = useState(initialYear);

  const shiftTypesMap = useMemo(() => Object.fromEntries(shiftTypes.map(item => [item.id, item])), [shiftTypes]);

  const availableYears = useMemo(() => {
    const years = new Set(events.map(e => new Date(e.date).getUTCFullYear()));
    years.add(initialYear);
    return Array.from(years).sort((a, b) => b - a);
  }, [events, initialYear]);
  
  const todayString = getTodayString();

  const yearData = useMemo(() => {
    const theoreticalCalendar = theoreticalCalendarService.getTheoreticalCalendar(selectedYear);
    const yearEvents = events.filter(e => e.date.startsWith(String(selectedYear)));
    
    // Data up to today (inclusive) for real-time KPIs
    const eventsUpToToday = yearEvents.filter(e => e.date <= todayString);
    
    // --- Hour Calculations ---
    const computedHoursUpToToday = eventsUpToToday.reduce((acc, event) => {
        const eventType = eventTypesMap[event.typeId];
        return acc + calculateEventNetHours(event, eventType);
    }, 0);

    let theoreticalHoursUpToToday = 0;
    let theoreticalHoursFuture = 0;

    for (const dateStr in theoreticalCalendar) {
        const shift = shiftTypesMap[theoreticalCalendar[dateStr]];
        if (shift) {
            if (dateStr <= todayString) {
                theoreticalHoursUpToToday += shift.hours;
            } else {
                theoreticalHoursFuture += shift.hours;
            }
        }
    }

    const totalTheoreticalHoursYear = theoreticalHoursUpToToday + theoreticalHoursFuture;
    const projectedComputedHoursYear = computedHoursUpToToday + theoreticalHoursFuture; // Approximation

    // --- KPI Calculations ---
    const balanceUpToToday = computedHoursUpToToday - theoreticalHoursUpToToday;
    const hourBank = Math.max(0, balanceUpToToday);
    const projectedAnnualBalance = projectedComputedHoursYear - totalTheoreticalHoursYear;

    const vacationsTaken = eventsUpToToday.filter(e => eventTypesMap[e.typeId]?.category === EventCategory.Vacaciones).length;
    const futurePlannedVacations = yearEvents.filter(e => e.date > todayString && eventTypesMap[e.typeId]?.category === EventCategory.Vacaciones).length;

    const annualPace = totalTheoreticalHoursYear > 0 ? (computedHoursUpToToday / totalTheoreticalHoursYear) * 100 : 0;

    // --- Chart Data ---
    const categoryDistributionData = eventsUpToToday.reduce((acc, event) => {
        const category = eventTypesMap[event.typeId]?.category || 'Sin Categoría';
        acc[category] = (acc[category] || 0) + 1;
        return acc;
    }, {} as Record<string, number>);

    const monthlyBalanceData = Array(12).fill(null).map((_, monthIndex) => {
        const currentMonthStr = `${selectedYear}-${String(monthIndex + 1).padStart(2, '0')}`;
        let theoretical = 0;
        let worked = 0;
        
        for (const dateStr in theoreticalCalendar) {
            if (dateStr.startsWith(currentMonthStr)) {
                const shift = shiftTypesMap[theoreticalCalendar[dateStr]];
                if (shift) theoretical += shift.hours;
            }
        }

        for (const event of yearEvents) {
            if (event.date.startsWith(currentMonthStr)) {
                 const eventType = eventTypesMap[event.typeId];
                 worked += calculateEventNetHours(event, eventType);
            }
        }
        return { theoretical, worked };
    });

    return {
      yearEvents,
      eventsUpToToday,
      kpis: {
        balanceUpToToday,
        hourBank,
        projectedAnnualBalance,
        vacationsTaken,
        futurePlannedVacations,
        annualPace,
        computedHoursUpToToday,
        theoreticalHoursUpToToday
      },
      charts: {
        categoryDistributionData,
        monthlyBalanceData,
      }
    };
  }, [selectedYear, events, todayString, eventTypesMap, shiftTypesMap]);
  
  if (!isOpen) return null;

  const colorPalette = ['blue-500', 'green-500', 'purple-500', 'yellow-500', 'red-500', 'pink-500', 'indigo-500'];
  const stringToColor = (str: string) => {
      let hash = 0;
      for (let i = 0; i < str.length; i++) { hash = str.charCodeAt(i) + ((hash << 5) - hash); }
      return colorPalette[Math.abs(hash) % colorPalette.length];
  };

  const pieChartData = Object.entries(yearData.charts.categoryDistributionData).map(([category, value]) => ({
      label: category,
      value,
      color: stringToColor(category),
  }));

  const barChartData = {
      labels: MONTH_NAMES,
      datasets: [
          { label: 'Horas Teóricas', data: yearData.charts.monthlyBalanceData.map(m => m.theoretical), color: 'bg-gray-300' },
          { label: 'Horas Computadas', data: yearData.charts.monthlyBalanceData.map(m => m.worked), color: 'bg-indigo-500' },
      ]
  };

  const KpiCard: React.FC<{title: string; value: string; color?: string; subtext?: string}> = ({title, value, color = 'text-gray-800', subtext}) => (
    <div className="bg-white p-5 rounded-lg shadow">
      <h4 className="text-gray-500 font-semibold truncate">{title}</h4>
      <p className={`text-3xl font-bold ${color}`}>{value}</p>
      {subtext && <p className="text-xs text-gray-400 mt-1">{subtext}</p>}
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 z-40 flex justify-center items-center p-4">
      <div className="bg-gray-100 rounded-xl shadow-2xl w-full max-w-6xl p-6 relative animate-fade-in-up flex flex-col" style={{ height: '95vh' }}>
        <div className="flex-shrink-0 flex justify-between items-center mb-6 pb-4 border-b border-gray-300">
          <h2 className="text-3xl font-bold text-gray-800">Panel de Estadísticas</h2>
          <div className="flex items-center space-x-4">
            <select
                value={selectedYear}
                onChange={(e) => setSelectedYear(Number(e.target.value))}
                className="block border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
                {availableYears.map(year => <option key={year} value={year}>{year}</option>)}
            </select>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-800">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
            </button>
          </div>
        </div>
        
        <div className="overflow-y-auto flex-grow pr-3">
          {yearData.yearEvents.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <p className="text-xl text-gray-500">No hay datos de eventos para el año {selectedYear}.</p>
            </div>
          ) : (
            <div className="space-y-6">
                <h3 className="text-xl font-bold text-gray-700">KPIs Clave</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
                   <KpiCard title="Balance a día de hoy" value={`${yearData.kpis.balanceUpToToday.toFixed(2)}h`} color={yearData.kpis.balanceUpToToday >= 0 ? 'text-green-600' : 'text-red-600'} subtext="Real vs Teórico hasta hoy" />
                   <KpiCard title="Banco de Horas" value={`${yearData.kpis.hourBank.toFixed(2)}h`} color="text-blue-600" subtext="Horas extra disponibles" />
                   <KpiCard title="Proyección Balance Anual" value={`${yearData.kpis.projectedAnnualBalance.toFixed(2)}h`} color={yearData.kpis.projectedAnnualBalance >= 0 ? 'text-blue-600' : 'text-orange-600'} subtext="Estimación a final de año" />
                   <KpiCard title="Vacaciones Disfrutadas" value={`${yearData.kpis.vacationsTaken}d`} color="text-green-600" subtext={`+${yearData.kpis.futurePlannedVacations}d planificadas`} />
                </div>
                
                <h3 className="text-xl font-bold text-gray-700 mt-6">Análisis de Horas</h3>
                 <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
                    <KpiCard title="Horas Teóricas (hasta hoy)" value={`${yearData.kpis.theoreticalHoursUpToToday.toFixed(2)}h`} />
                    <KpiCard title="Horas Computadas (hasta hoy)" value={`${yearData.kpis.computedHoursUpToToday.toFixed(2)}h`} />
                    <KpiCard title="Ritmo Anual (Proyectado)" value={`${yearData.kpis.annualPace.toFixed(1)}%`} subtext="Porcentaje completado de la jornada teórica anual" />
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 mt-6">
                    <div className="lg:col-span-2 bg-white p-5 rounded-lg shadow">
                      <PieChart title="Distribución por Categoría (hasta hoy)" data={pieChartData} />
                    </div>
                    <div className="lg:col-span-3 bg-white p-5 rounded-lg shadow">
                       <BarChart title="Evolución Mensual de Horas" data={barChartData} />
                    </div>
                </div>

                <div className="bg-white p-6 rounded-lg shadow text-center mt-6">
                    <h3 className="text-xl font-bold text-gray-700 mb-2">¿Necesitas un análisis más profundo?</h3>
                    <p className="text-gray-600 mb-4">Utiliza la IA de Gemini para obtener conclusiones y recomendaciones basadas en tus datos anuales.</p>
                    <button onClick={onShowAdvancedStats} className="bg-indigo-600 text-white font-bold py-2 px-6 rounded-lg hover:bg-indigo-700 transition duration-300 inline-flex items-center space-x-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" /></svg>
                        <span>Análisis Avanzado con IA</span>
                    </button>
                </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StatsDashboard;
