import React, { useState, useMemo } from 'react';
import Header from './components/Header';
import Calendar from './components/Calendar';
import Summary from './components/Summary';
import EventModal from './components/EventModal';
import PlanningMode from './components/PlanningMode';
import PlanningDayModal from './components/PlanningDayModal';
import DataExport from './components/DataExport';
import AdvancedStats from './components/AdvancedStats';
import StatsDashboard from './components/StatsDashboard';
import SettingsModal from './components/SettingsModal';
import { useEvents } from './hooks/useEvents';
import { useTheoreticalCalendar } from './hooks/useTheoreticalCalendar';
import { useAppConfig } from './hooks/useAppConfig';
import { Event, ComputationType } from './types';

const getUTCDate = (date: Date): Date => {
  return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
};

const JORNADA_LABORAL_ID = 'jornada_laboral';

function App() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [isEventModalOpen, setIsEventModalOpen] = useState(false);
  const [isPlanningMode, setIsPlanningMode] = useState(false);
  const [planningDate, setPlanningDate] = useState<Date | null>(null);
  const [isPlanningModalOpen, setIsPlanningModalOpen] = useState(false);
  const [isStatsModalOpen, setIsStatsModalOpen] = useState(false);
  const [isStatsDashboardOpen, setIsStatsDashboardOpen] = useState(false);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);

  const { events, addEvent, updateEvent, deleteEvent } = useEvents();
  const config = useAppConfig();
  const { eventTypes, shiftTypes } = config;

  const eventTypesMap = useMemo(() => Object.fromEntries(eventTypes.map(item => [item.id, item])), [eventTypes]);
  const shiftTypesMap = useMemo(() => Object.fromEntries(shiftTypes.map(item => [item.id, item])), [shiftTypes]);
  
  const { calendar: theoreticalCalendar, updateDayPlan, bulkUpdateMonth } = useTheoreticalCalendar(currentDate.getFullYear());

  const currentMonthEvents = useMemo(() => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    return events.filter(e => {
      const eventDate = new Date(e.date);
      return eventDate.getUTCFullYear() === year && eventDate.getUTCMonth() === month;
    });
  }, [events, currentDate]);
  
  const handlePrevMonth = () => setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1));
  const handleNextMonth = () => setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1));
  const handleToday = () => setCurrentDate(new Date());

  const handleDayClick = (date: Date) => {
    if (isPlanningMode) {
      setPlanningDate(date);
      setIsPlanningModalOpen(true);
    } else {
      setSelectedDate(date);
      setSelectedEvent(null);
      setIsEventModalOpen(true);
    }
  };

  const handleEventClick = (event: Event) => {
    setSelectedEvent(event);
    setSelectedDate(new Date(`${event.date}T00:00:00Z`));
    setIsEventModalOpen(true);
  };

  const handleCloseEventModal = () => {
    setIsEventModalOpen(false);
    setSelectedEvent(null);
    setSelectedDate(null);
  };

  const handleSaveEvent = (eventData: Omit<Event, 'id'> | Event) => {
    const isUpdate = 'id' in eventData;
    const date = eventData.date;

    const otherDayEvents = events.filter(e => e.date === date && (isUpdate ? e.id !== eventData.id : true));
    
    const newEventType = eventTypesMap[eventData.typeId];
    if (!newEventType) {
        alert('Error: Tipo de evento no válido.');
        return;
    }

    const newEventIsBlocking = newEventType.computationType === ComputationType.Neutral && !newEventType.defaultStartTime;
    const existingEventIsBlocking = otherDayEvents.some(e => eventTypesMap[e.typeId]?.computationType === ComputationType.Neutral && !eventTypesMap[e.typeId]?.defaultStartTime);

    if (newEventIsBlocking && otherDayEvents.length > 0) {
        alert('Error: No se puede añadir un evento de día completo (como Vacaciones) a una fecha que ya tiene otros eventos.');
        return;
    }

    if (existingEventIsBlocking) {
        alert('Error: No se puede añadir un nuevo evento a una fecha que ya tiene un evento de día completo.');
        return;
    }
    
    const isWorkShift = newEventType.category === 'Trabajo';
    if (isWorkShift && eventData.shiftId) {
      const hasConflict = otherDayEvents.some(
        e => eventTypesMap[e.typeId]?.category === 'Trabajo' && e.shiftId === eventData.shiftId
      );
      if (hasConflict) {
        const shiftLabel = shiftTypesMap[eventData.shiftId]?.label || 'este turno';
        alert(`Error: Ya existe un turno de '${shiftLabel}' en esta fecha.`);
        return;
      }
    }

    if (!isUpdate && otherDayEvents.length > 0) {
      if (!window.confirm('Ya existen otros eventos en esta fecha. ¿Deseas añadir este nuevo evento?')) {
        return;
      }
    }
    
    if (isUpdate) {
      updateEvent(eventData);
    } else {
      addEvent(eventData);
    }
    handleCloseEventModal();
  };

  const handleDeleteEvent = (eventId: string) => {
    deleteEvent(eventId);
    handleCloseEventModal();
  };

  const handleClosePlanningModal = () => {
    setIsPlanningModalOpen(false);
    setPlanningDate(null);
  };

  const handleSavePlanningDay = (shiftId: string | null) => {
    if (planningDate) {
      const dateString = getUTCDate(planningDate).toISOString().split('T')[0];
      updateDayPlan(dateString, shiftId);
    }
    handleClosePlanningModal();
  };

  const handleBulkUpdate = (shiftId: string, days: 'weekdays' | 'weekends' | 'all') => {
      bulkUpdateMonth(currentDate.getMonth(), shiftId, days);
  };

  return (
    <div className="bg-gray-100 min-h-screen font-sans text-gray-800 p-4 sm:p-8">
      <div className="max-w-7xl mx-auto">
        <Header 
          currentDate={currentDate}
          onPrevMonth={handlePrevMonth}
          onNextMonth={handleNextMonth}
          onToday={handleToday}
          onShowDashboard={() => setIsStatsDashboardOpen(true)}
          onShowSettings={() => setIsSettingsModalOpen(true)}
        />
        <main className="mt-8 grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
             <Calendar 
              currentDate={currentDate}
              events={events}
              onDayClick={handleDayClick}
              onEventClick={handleEventClick}
              isPlanningMode={isPlanningMode}
              theoreticalCalendar={theoreticalCalendar}
              eventTypesMap={eventTypesMap}
              shiftTypesMap={shiftTypesMap}
            />
          </div>
          <aside>
            <PlanningMode
              isPlanningMode={isPlanningMode}
              onTogglePlanningMode={setIsPlanningMode}
              onBulkUpdate={handleBulkUpdate}
              shiftTypes={shiftTypes}
            />
            <Summary 
              events={currentMonthEvents} 
              month={currentDate.getMonth()}
              year={currentDate.getFullYear()}
              theoreticalCalendar={theoreticalCalendar}
              eventTypesMap={eventTypesMap}
              shiftTypesMap={shiftTypesMap}
            />
            <DataExport 
              events={events}
              theoreticalCalendar={theoreticalCalendar}
              year={currentDate.getFullYear()}
              eventTypesMap={eventTypesMap}
              shiftTypesMap={shiftTypesMap}
            />
          </aside>
        </main>
      </div>

      {isEventModalOpen && (
        <EventModal
          isOpen={isEventModalOpen}
          onClose={handleCloseEventModal}
          onSave={handleSaveEvent}
          onDelete={handleDeleteEvent}
          event={selectedEvent}
          date={selectedDate}
          eventTypes={eventTypes}
          shiftTypes={shiftTypes}
          eventTypesMap={eventTypesMap}
          shiftTypesMap={shiftTypesMap}
        />
      )}

      {isPlanningModalOpen && (
        <PlanningDayModal 
            isOpen={isPlanningModalOpen}
            onClose={handleClosePlanningModal}
            onSave={handleSavePlanningDay}
            date={planningDate}
            currentPlan={planningDate ? theoreticalCalendar[getUTCDate(planningDate).toISOString().split('T')[0]] || null : null}
            shiftTypes={shiftTypes}
        />
      )}

      {isStatsDashboardOpen && (
        <StatsDashboard
          isOpen={isStatsDashboardOpen}
          onClose={() => setIsStatsDashboardOpen(false)}
          events={events}
          initialYear={currentDate.getFullYear()}
          onShowAdvancedStats={() => setIsStatsModalOpen(true)}
          eventTypes={eventTypes}
          shiftTypes={shiftTypes}
          eventTypesMap={eventTypesMap}
        />
      )}

      {isStatsModalOpen && (
        <AdvancedStats 
          isOpen={isStatsModalOpen}
          onClose={() => setIsStatsModalOpen(false)}
          events={events}
          theoreticalCalendar={theoreticalCalendar}
          year={currentDate.getFullYear()}
          eventTypesMap={eventTypesMap}
        />
      )}

      {isSettingsModalOpen && (
          <SettingsModal 
            isOpen={isSettingsModalOpen}
            onClose={() => setIsSettingsModalOpen(false)}
            config={config}
          />
      )}
    </div>
  );
}

export default App;