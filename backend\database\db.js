import knex from 'knex';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs/promises';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Database configuration
const dbConfig = {
  client: 'sqlite3',
  connection: {
    filename: path.join(__dirname, '../../database/control_jornada.db')
  },
  useNullAsDefault: true,
  pool: {
    afterCreate: (conn, cb) => {
      conn.run('PRAGMA foreign_keys = ON', cb);
    }
  }
};

// Create database instance
export const db = knex(dbConfig);

// Initialize database with schema
export async function initializeDatabase() {
  try {
    // Ensure database directory exists
    const dbDir = path.dirname(dbConfig.connection.filename);
    await fs.mkdir(dbDir, { recursive: true });

    // Read and execute schema
    const schemaPath = path.join(__dirname, '../../database/schema.sql');
    const schema = await fs.readFile(schemaPath, 'utf8');
    
    // Split schema into individual statements and execute
    const statements = schema
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    for (const statement of statements) {
      await db.raw(statement);
    }

    console.log('✅ Database initialized successfully');
    
    // Check if we need to seed default data
    await seedDefaultData();
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    throw error;
  }
}

// Seed default data if tables are empty
async function seedDefaultData() {
  try {
    // Check if we have any users
    const userCount = await db('users').count('id as count').first();
    
    if (userCount.count === 0) {
      console.log('📦 Seeding default data...');
      
      // Create default admin user (password: admin123)
      const bcrypt = await import('bcryptjs');
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      const [userId] = await db('users').insert({
        username: 'admin',
        email: '<EMAIL>',
        password_hash: hashedPassword,
        full_name: 'Administrador'
      });

      // Insert default event types
      const defaultEventTypes = [
        { id: 'jornada_laboral', label: 'Jornada Laboral', color: 'blue-500', category: 'Trabajo', computation_type: 'adds', break_time: '00:15', is_deletable: 0 },
        { id: 'vacaciones', label: 'Vacaciones', color: 'green-500', category: 'Vacaciones', computation_type: 'neutral', break_time: '00:00', is_deletable: 0 },
        { id: 'formacion', label: 'Formación', color: 'purple-500', category: 'Formación', computation_type: 'adds', break_time: '00:00', default_start_time: '09:00', default_end_time: '13:00', is_deletable: 0 },
        { id: 'permiso', label: 'Permiso', color: 'yellow-500', category: 'Permiso', computation_type: 'neutral', break_time: '00:00', is_deletable: 0 },
        { id: 'baja_medica', label: 'Baja Médica', color: 'red-500', category: 'Ausencia', computation_type: 'neutral', break_time: '00:00', is_deletable: 0 },
        { id: 'asunto_propio', label: 'Asunto Propio', color: 'gray-500', category: 'Permiso', computation_type: 'subtracts', break_time: '00:00', is_deletable: 0 },
        { id: 'horas_compensadas', label: 'Horas Compensadas', color: 'cyan-500', category: 'Permiso', computation_type: 'subtracts', break_time: '00:00', is_deletable: 0 }
      ];

      for (const eventType of defaultEventTypes) {
        await db('event_types').insert({
          ...eventType,
          user_id: userId
        });
      }

      // Insert default shift types
      const defaultShiftTypes = [
        { id: 'manana', label: 'Mañana', icon: '☀️', time_range: '06:00-14:00', hours: 7.75, break_time: '00:15', is_deletable: 0 },
        { id: 'tarde', label: 'Tarde', icon: '🌙', time_range: '14:00-22:00', hours: 7.75, break_time: '00:15', is_deletable: 0 },
        { id: 'noche', label: 'Noche', icon: '🦉', time_range: '22:00-06:00', hours: 7.75, break_time: '00:15', is_deletable: 0 },
        { id: 'festivo_manana', label: 'Festivo Mañana', icon: '🎉', time_range: '06:00-18:00', hours: 11, break_time: '01:00', is_deletable: 0 },
        { id: 'festivo_noche', label: 'Festivo Noche', icon: '🎆', time_range: '18:00-06:00', hours: 11, break_time: '01:00', is_deletable: 0 }
      ];

      for (const shiftType of defaultShiftTypes) {
        await db('shift_types').insert({
          ...shiftType,
          user_id: userId
        });
      }

      console.log('✅ Default data seeded successfully');
      console.log('👤 Default admin user created: admin / admin123');
    }
  } catch (error) {
    console.error('❌ Error seeding default data:', error);
  }
}

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('🔄 Closing database connection...');
  await db.destroy();
  process.exit(0);
});

export default db;
