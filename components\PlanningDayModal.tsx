import React, { useState, useEffect } from 'react';
import type { ShiftTypeConfigItem } from '../types';

interface PlanningDayModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (shiftId: string | null) => void;
  date: Date | null;
  currentPlan: string | null;
  shiftTypes: ShiftTypeConfigItem[];
}

const PlanningDayModal: React.FC<PlanningDayModalProps> = ({ isOpen, onClose, onSave, date, currentPlan, shiftTypes }) => {
  const [selectedShiftId, setSelectedShiftId] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      setSelectedShiftId(currentPlan);
    }
  }, [isOpen, currentPlan]);

  const handleSave = () => {
    onSave(selectedShiftId);
  };

  if (!isOpen || !date) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md p-6 relative animate-fade-in-up">
        <button onClick={onClose} className="absolute top-3 right-3 text-gray-500 hover:text-gray-800">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
        </button>
        <h2 className="text-2xl font-bold mb-2 text-gray-800">Planificar Turno</h2>
        <p className="text-lg text-gray-600 mb-6">{date.toLocaleDateString('es-ES', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</p>
        
        <div className="space-y-3">
          {shiftTypes.map(shift => (
            <label key={shift.id} className={`flex items-center p-3 rounded-lg border-2 cursor-pointer transition-colors ${selectedShiftId === shift.id ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:bg-gray-50'}`}>
              <input 
                type="radio" 
                name="shift" 
                className="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"
                value={shift.id} 
                checked={selectedShiftId === shift.id}
                onChange={() => setSelectedShiftId(shift.id)}
              />
              <span className="ml-3 font-medium text-gray-700">{shift.icon} {shift.label} ({shift.time})</span>
            </label>
          ))}
            <label className={`flex items-center p-3 rounded-lg border-2 cursor-pointer transition-colors ${selectedShiftId === null ? 'border-red-500 bg-red-50' : 'border-gray-200 hover:bg-gray-50'}`}>
              <input 
                type="radio" 
                name="shift" 
                className="h-4 w-4 text-red-600 border-gray-300 focus:ring-red-500"
                checked={selectedShiftId === null}
                onChange={() => setSelectedShiftId(null)}
              />
              <span className="ml-3 font-medium text-red-700">Sin turno (Día libre)</span>
            </label>
        </div>

        <div className="mt-8 flex justify-end space-x-3">
          <button type="button" onClick={onClose} className="rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            Cancelar
          </button>
          <button onClick={handleSave} className="rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            Guardar Plan
          </button>
        </div>
      </div>
    </div>
  );
};

export default PlanningDayModal;
