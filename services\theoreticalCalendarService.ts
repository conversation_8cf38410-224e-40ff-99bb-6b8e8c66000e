import { theoreticalCalendarApi, getAuthToken } from './apiService';

export type TheoreticalCalendar = Record<string, string>; // YYYY-MM-DD -> shiftId

const getStorageKey = (year: number) => `theoreticalCalendar_${year}`;

const getTheoreticalCalendarFromLocalStorage = (year: number): TheoreticalCalendar => {
  try {
    const calendarJson = localStorage.getItem(getStorageKey(year));
    return calendarJson ? JSON.parse(calendarJson) : {};
  } catch (error) {
    console.error(`Error al leer el calendario teórico para ${year}:`, error);
    return {};
  }
};

export const getTheoreticalCalendar = async (year: number): Promise<TheoreticalCalendar> => {
  try {
    if (getAuthToken()) {
      // Use API if authenticated
      return await theoreticalCalendarApi.getYear(year);
    } else {
      // Fallback to localStorage
      return getTheoreticalCalendarFromLocalStorage(year);
    }
  } catch (error) {
    console.error(`Error al obtener el calendario teórico para ${year}:`, error);
    // Fallback to localStorage on API error
    return getTheoreticalCalendarFromLocalStorage(year);
  }
};

const saveTheoreticalCalendarToLocalStorage = (year: number, calendar: TheoreticalCalendar): void => {
  try {
    localStorage.setItem(getStorageKey(year), JSON.stringify(calendar));
  } catch (error) {
    console.error(`Error al guardar el calendario teórico para ${year}:`, error);
  }
};

export const saveTheoreticalCalendar = async (year: number, calendar: TheoreticalCalendar): Promise<void> => {
  try {
    if (getAuthToken()) {
      // Use API if authenticated - convert calendar object to entries array
      const entries = Object.entries(calendar).map(([date, shiftId]) => ({ date, shiftId }));
      await theoreticalCalendarApi.bulkUpdate(entries);
    } else {
      // Fallback to localStorage
      saveTheoreticalCalendarToLocalStorage(year, calendar);
    }
  } catch (error) {
    console.error(`Error al guardar el calendario teórico para ${year}:`, error);
    // Fallback to localStorage on API error
    saveTheoreticalCalendarToLocalStorage(year, calendar);
  }
};
