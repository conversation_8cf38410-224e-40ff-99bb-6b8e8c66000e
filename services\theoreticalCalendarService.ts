export type TheoreticalCalendar = Record<string, string>; // YYYY-MM-DD -> shiftId

const getStorageKey = (year: number) => `theoreticalCalendar_${year}`;

export const getTheoreticalCalendar = (year: number): TheoreticalCalendar => {
  try {
    const calendarJson = localStorage.getItem(getStorageKey(year));
    return calendarJson ? JSON.parse(calendarJson) : {};
  } catch (error) {
    console.error(`Error al leer el calendario teórico para ${year}:`, error);
    return {};
  }
};

export const saveTheoreticalCalendar = (year: number, calendar: TheoreticalCalendar): void => {
  try {
    localStorage.setItem(getStorageKey(year), JSON.stringify(calendar));
  } catch (error) {
    console.error(`Error al guardar el calendario teórico para ${year}:`, error);
  }
};
