import React, { useState, useEffect, useMemo } from 'react';
import type { Event, EventTypeConfigItem } from '../types';
import type { TheoreticalCalendar } from '../services/theoreticalCalendarService';
import { GoogleGenAI } from "@google/genai";

interface AdvancedStatsProps {
  isOpen: boolean;
  onClose: () => void;
  events: Event[];
  theoreticalCalendar: TheoreticalCalendar;
  year: number;
  eventTypesMap: Record<string, EventTypeConfigItem>;
}

const AdvancedStats: React.FC<AdvancedStatsProps> = ({ isOpen, onClose, events, theoreticalCalendar, year, eventTypesMap }) => {
  const [analysis, setAnalysis] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const yearEvents = useMemo(() => events.filter(e => e.date.startsWith(String(year))), [events, year]);

  const generateAnalysis = async () => {
    setIsLoading(true);
    setError('');
    setAnalysis('');

    if (yearEvents.length === 0 && Object.keys(theoreticalCalendar).length === 0) {
        setAnalysis("No hay datos para el año seleccionado. No se puede generar un análisis.");
        setIsLoading(false);
        return;
    }
    
    try {
      if (!process.env.API_KEY) {
        throw new Error("La clave de API para Gemini no está configurada.");
      }
      const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });
      
      const prompt = `
        Eres un asistente experto en análisis de datos de recursos humanos.
        Analiza los siguientes datos de control de jornada para el año ${year} de un trabajador.
        Proporciona un resumen conciso y luego destaca patrones interesantes, anomalías o áreas de mejora.

        **Contexto de los Datos:**
        - **Calendario Teórico**: Planificación de turnos (lo que se esperaba que sucediera).
        - **Eventos Reales**: Registros de lo que realmente ocurrió (jornadas, vacaciones, etc.).
        - **Tipos de Evento**: Cada evento tiene una 'category' y un 'computationType'.
          - 'computationType: "adds"': Las horas de este evento suman al total de horas trabajadas (ej. Jornada Laboral, Formación).
          - 'computationType: "subtracts"': Las horas de este evento restan al total (ej. Permiso recuperable).
          - 'computationType: "neutral"': El evento no afecta al cómputo de horas (ej. Vacaciones, Baja Médica).
        
        **Datos a Analizar:**
        Calendario Teórico: ${JSON.stringify(theoreticalCalendar, null, 2)}
        Eventos Reales: ${JSON.stringify(yearEvents.map(e => ({...e, eventType: eventTypesMap[e.typeId] })), null, 2)}

        **Enfoque del Análisis:**
        1.  **Balance de Horas**: Compara las horas teóricas con las horas computadas reales ('adds' - 'subtracts'). ¿Hay un déficit o superávit significativo? ¿A qué se puede deber?
        2.  **Distribución de Eventos por Categoría**: Analiza la proporción de tiempo dedicado a 'Trabajo', 'Vacaciones', 'Permisos', 'Formación' y 'Ausencias'. ¿Parece una distribución equilibrada y saludable?
        3.  **Consistencia y Desviaciones**: Compara el calendario teórico con el real. ¿Se cumplen los turnos planificados? ¿Hay muchos eventos imprevistos o cambios de última hora?
        4.  **Recomendaciones Clave**: Basado en todo lo anterior, ofrece 2-3 recomendaciones prácticas para mejorar la planificación, el balance vida-trabajo o la eficiencia.

        Formatea tu respuesta usando Markdown para una fácil lectura, con títulos claros, listas y negritas.
      `;
      
      const response = await ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: prompt
      });

      setAnalysis(response.text);

    } catch (e) {
      console.error("Error generating analysis:", e);
      const errorMessage = "No se pudo generar el análisis. Por favor, revisa la configuración de la API key o inténtalo de nuevo más tarde.";
      setError(errorMessage);
      if (e instanceof Error) {
        setAnalysis(`Error: ${e.message}`);
      } else {
        setAnalysis('Ocurrió un error desconocido.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      generateAnalysis();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-3xl p-6 relative animate-fade-in-up flex flex-col" style={{ maxHeight: '90vh' }}>
        <div className="flex-shrink-0">
          <button onClick={onClose} className="absolute top-3 right-3 text-gray-500 hover:text-gray-800">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
          <h2 className="text-2xl font-bold mb-4 text-gray-800">Análisis Avanzado con IA ({year})</h2>
        </div>
        
        <div className="overflow-y-auto flex-grow pr-2">
          {isLoading && <div className="text-center p-8"><p className="text-lg text-indigo-600 animate-pulse">🧠 Analizando datos, por favor espera...</p></div>}
          {error && <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4" role="alert"><p className="font-bold">Error</p><p>{error}</p></div>}
          {analysis && !isLoading && (
            <div className="bg-gray-50 p-4 rounded-md border text-sm text-gray-800">
                <pre className="whitespace-pre-wrap font-sans">{analysis}</pre>
            </div>
          )}
        </div>

        <div className="mt-6 flex-shrink-0 flex justify-between items-center">
          <p className="text-xs text-gray-400">Powered by Gemini</p>
          <div className="flex space-x-3">
              <button type="button" onClick={generateAnalysis} disabled={isLoading} className="rounded-md border border-indigo-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-indigo-700 hover:bg-indigo-50 disabled:opacity-50 disabled:cursor-not-allowed">
                {isLoading ? 'Generando...' : 'Volver a generar'}
              </button>
              <button type="button" onClick={onClose} className="rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50">
                Cerrar
              </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedStats;