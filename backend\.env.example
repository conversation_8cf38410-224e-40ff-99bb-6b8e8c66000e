# Server Configuration
PORT=3001
NODE_ENV=development

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:5174

# JWT Secret (change this in production!)
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# Database Configuration (SQLite by default)
DB_CLIENT=sqlite3
DB_FILENAME=../database/control_jornada.db

# For PostgreSQL (uncomment and configure if using PostgreSQL)
# DB_CLIENT=pg
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=control_jornada
# DB_USER=postgres
# DB_PASSWORD=your_password

# API Keys (if needed)
GEMINI_API_KEY=your_gemini_api_key_here
