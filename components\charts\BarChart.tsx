import React from 'react';

interface BarChartDataset {
  label: string;
  data: number[];
  color: string;
}

interface BarChartProps {
  title: string;
  data: {
    labels: string[];
    datasets: BarChartDataset[];
  };
}

const PALETTE: { [key: string]: string } = {
  'bg-gray-300': '#d1d5db',
  'bg-indigo-500': '#6366f1',
  default: '#9ca3af',
};

const BarChart: React.FC<BarChartProps> = ({ title, data }) => {
  const maxValue = Math.max(...data.datasets.flatMap(ds => ds.data));
  const yAxisMax = Math.ceil(maxValue / 50) * 50 || 50; // Round up to nearest 50

  return (
    <div className="h-full flex flex-col">
      <h3 className="text-lg font-bold text-gray-700 mb-2 text-center">{title}</h3>
      <div className="flex-grow flex flex-col">
        {/* Y Axis Labels */}
        <div className="flex-grow flex relative">
            <div className="w-10 text-right pr-2 text-xs text-gray-500 flex flex-col justify-between">
                <span>{yAxisMax}h</span>
                <span>{yAxisMax / 2}h</span>
                <span>0h</span>
            </div>
            {/* Chart Area */}
            <div className="flex-grow grid grid-cols-12 gap-2 border-l border-b border-gray-200 relative">
                {/* Grid Lines */}
                <div className="absolute top-0 left-0 w-full h-full">
                    <div className="h-1/2 border-b border-dashed border-gray-200"></div>
                </div>
                {data.labels.map((label, index) => (
                    <div key={label} className="flex justify-around items-end h-full relative group">
                        {data.datasets.map(dataset => (
                            <div
                                key={dataset.label}
                                className="w-2/3 transition-all duration-300"
                                style={{
                                    height: `${(dataset.data[index] / yAxisMax) * 100}%`,
                                    backgroundColor: PALETTE[dataset.color] || PALETTE.default,
                                }}
                            >
                                <div className="absolute -top-7 left-1/2 -translate-x-1/2 hidden group-hover:block bg-gray-800 text-white text-xs rounded py-1 px-2 pointer-events-none whitespace-nowrap">
                                    {dataset.label}: {dataset.data[index].toFixed(2)}h
                                </div>
                            </div>
                        ))}
                    </div>
                ))}
            </div>
        </div>
        {/* X Axis Labels */}
        <div className="w-full flex pl-10">
             <div className="flex-grow grid grid-cols-12 gap-2 text-center text-xs text-gray-500 pt-1">
                 {data.labels.map(label => <span key={label}>{label.substring(0,3)}</span>)}
             </div>
        </div>
         {/* Legend */}
        <div className="flex justify-center space-x-4 mt-4 text-sm text-gray-600">
            {data.datasets.map(dataset => (
                <div key={dataset.label} className="flex items-center">
                    <span className={`w-3 h-3 rounded-full mr-2 ${dataset.color}`}></span>
                    <span>{dataset.label}</span>
                </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default BarChart;
