import express from 'express';
import { body, validationResult } from 'express-validator';
import { db } from '../database/db.js';
import { authenticateToken } from '../middleware/auth.js';
import { v4 as uuidv4 } from 'uuid';

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// Migrate data from localStorage to database
router.post('/from-localstorage', [
  body('events').optional().isArray(),
  body('eventTypes').optional().isArray(),
  body('shiftTypes').optional().isArray(),
  body('theoreticalCalendars').optional().isObject()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { events = [], eventTypes = [], shiftTypes = [], theoreticalCalendars = {} } = req.body;
    const migrationResults = {
      events: { created: 0, skipped: 0, errors: [] },
      eventTypes: { created: 0, updated: 0, skipped: 0, errors: [] },
      shiftTypes: { created: 0, updated: 0, skipped: 0, errors: [] },
      theoreticalCalendar: { created: 0, updated: 0, errors: [] }
    };

    await db.transaction(async (trx) => {
      // 1. Migrate Event Types
      for (const eventType of eventTypes) {
        try {
          const existingEventType = await trx('event_types')
            .where('id', eventType.id)
            .where('user_id', req.user.id)
            .first();

          const eventTypeData = {
            id: eventType.id,
            user_id: req.user.id,
            label: eventType.label,
            category: eventType.category,
            computation_type: eventType.computationType,
            color: eventType.color,
            default_start_time: eventType.defaultStartTime || null,
            default_end_time: eventType.defaultEndTime || null,
            break_time: eventType.breakTime || '00:00',
            default_shift_id: eventType.defaultShiftId || null,
            is_deletable: eventType.isDeletable !== false
          };

          if (existingEventType) {
            await trx('event_types')
              .where('id', eventType.id)
              .where('user_id', req.user.id)
              .update(eventTypeData);
            migrationResults.eventTypes.updated++;
          } else {
            await trx('event_types').insert(eventTypeData);
            migrationResults.eventTypes.created++;
          }
        } catch (error) {
          migrationResults.eventTypes.errors.push({
            id: eventType.id,
            error: error.message
          });
        }
      }

      // 2. Migrate Shift Types
      for (const shiftType of shiftTypes) {
        try {
          const existingShiftType = await trx('shift_types')
            .where('id', shiftType.id)
            .where('user_id', req.user.id)
            .first();

          const shiftTypeData = {
            id: shiftType.id,
            user_id: req.user.id,
            label: shiftType.label,
            icon: shiftType.icon,
            time_range: shiftType.time,
            hours: shiftType.hours,
            break_time: shiftType.breakTime || '00:00',
            is_deletable: shiftType.isDeletable !== false
          };

          if (existingShiftType) {
            await trx('shift_types')
              .where('id', shiftType.id)
              .where('user_id', req.user.id)
              .update(shiftTypeData);
            migrationResults.shiftTypes.updated++;
          } else {
            await trx('shift_types').insert(shiftTypeData);
            migrationResults.shiftTypes.created++;
          }
        } catch (error) {
          migrationResults.shiftTypes.errors.push({
            id: shiftType.id,
            error: error.message
          });
        }
      }

      // 3. Migrate Events
      for (const event of events) {
        try {
          // Check if event already exists
          const existingEvent = await trx('events')
            .where('id', event.id)
            .where('user_id', req.user.id)
            .first();

          if (existingEvent) {
            migrationResults.events.skipped++;
            continue;
          }

          // Verify event type exists
          const eventTypeExists = await trx('event_types')
            .where('id', event.typeId)
            .where('user_id', req.user.id)
            .first();

          if (!eventTypeExists) {
            migrationResults.events.errors.push({
              id: event.id,
              error: `Event type '${event.typeId}' not found`
            });
            continue;
          }

          // Verify shift type exists (if provided)
          if (event.shiftId) {
            const shiftTypeExists = await trx('shift_types')
              .where('id', event.shiftId)
              .where('user_id', req.user.id)
              .first();

            if (!shiftTypeExists) {
              migrationResults.events.errors.push({
                id: event.id,
                error: `Shift type '${event.shiftId}' not found`
              });
              continue;
            }
          }

          const eventData = {
            id: event.id || uuidv4(),
            user_id: req.user.id,
            date: event.date,
            type_id: event.typeId,
            title: event.title || null,
            shift_id: event.shiftId || null,
            start_time: event.startTime || null,
            end_time: event.endTime || null,
            description: event.description || null,
            break_time: event.breakTime || null
          };

          await trx('events').insert(eventData);
          migrationResults.events.created++;

        } catch (error) {
          migrationResults.events.errors.push({
            id: event.id || 'unknown',
            error: error.message
          });
        }
      }

      // 4. Migrate Theoretical Calendars
      for (const [year, calendar] of Object.entries(theoreticalCalendars)) {
        for (const [date, shiftId] of Object.entries(calendar)) {
          try {
            // Verify shift type exists
            const shiftTypeExists = await trx('shift_types')
              .where('id', shiftId)
              .where('user_id', req.user.id)
              .first();

            if (!shiftTypeExists) {
              migrationResults.theoreticalCalendar.errors.push({
                date,
                error: `Shift type '${shiftId}' not found`
              });
              continue;
            }

            // Check if entry already exists
            const existingEntry = await trx('theoretical_calendar')
              .where('user_id', req.user.id)
              .where('date', date)
              .first();

            if (existingEntry) {
              await trx('theoretical_calendar')
                .where('user_id', req.user.id)
                .where('date', date)
                .update({ shift_id: shiftId });
              migrationResults.theoreticalCalendar.updated++;
            } else {
              await trx('theoretical_calendar').insert({
                user_id: req.user.id,
                date,
                shift_id: shiftId
              });
              migrationResults.theoreticalCalendar.created++;
            }

          } catch (error) {
            migrationResults.theoreticalCalendar.errors.push({
              date,
              error: error.message
            });
          }
        }
      }
    });

    res.json({
      message: 'Migration completed',
      results: migrationResults,
      summary: {
        totalEventTypes: migrationResults.eventTypes.created + migrationResults.eventTypes.updated,
        totalShiftTypes: migrationResults.shiftTypes.created + migrationResults.shiftTypes.updated,
        totalEvents: migrationResults.events.created,
        totalCalendarEntries: migrationResults.theoreticalCalendar.created + migrationResults.theoreticalCalendar.updated,
        totalErrors: migrationResults.events.errors.length + 
                    migrationResults.eventTypes.errors.length + 
                    migrationResults.shiftTypes.errors.length + 
                    migrationResults.theoreticalCalendar.errors.length
      }
    });

  } catch (error) {
    console.error('Migration error:', error);
    res.status(500).json({ error: 'Migration failed', details: error.message });
  }
});

// Export current user data (for backup or transfer)
router.get('/export', async (req, res) => {
  try {
    const [events, eventTypes, shiftTypes, theoreticalCalendar] = await Promise.all([
      // Events
      db('events')
        .where('user_id', req.user.id)
        .select(
          'id', 'date', 'type_id as typeId', 'title', 'shift_id as shiftId',
          'start_time as startTime', 'end_time as endTime', 'description', 'break_time as breakTime'
        ),
      
      // Event Types
      db('event_types')
        .where('user_id', req.user.id)
        .select(
          'id', 'label', 'category', 'computation_type as computationType', 'color',
          'default_start_time as defaultStartTime', 'default_end_time as defaultEndTime',
          'break_time as breakTime', 'default_shift_id as defaultShiftId', 'is_deletable as isDeletable'
        ),
      
      // Shift Types
      db('shift_types')
        .where('user_id', req.user.id)
        .select(
          'id', 'label', 'icon', 'time_range as time', 'hours',
          'break_time as breakTime', 'is_deletable as isDeletable'
        ),
      
      // Theoretical Calendar
      db('theoretical_calendar')
        .where('user_id', req.user.id)
        .select('date', 'shift_id as shiftId')
    ]);

    // Group theoretical calendar by year
    const theoreticalCalendars = {};
    theoreticalCalendar.forEach(entry => {
      const year = entry.date.substring(0, 4);
      if (!theoreticalCalendars[year]) {
        theoreticalCalendars[year] = {};
      }
      theoreticalCalendars[year][entry.date] = entry.shiftId;
    });

    res.json({
      exportDate: new Date().toISOString(),
      user: {
        id: req.user.id,
        username: req.user.username,
        email: req.user.email
      },
      data: {
        events,
        eventTypes,
        shiftTypes,
        theoreticalCalendars
      }
    });

  } catch (error) {
    console.error('Export error:', error);
    res.status(500).json({ error: 'Export failed' });
  }
});

export default router;
