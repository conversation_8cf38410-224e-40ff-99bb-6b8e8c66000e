// Database configuration
const path = require('path');

const config = {
  development: {
    client: 'sqlite3',
    connection: {
      filename: path.join(__dirname, 'control_jornada.db')
    },
    useNullAsDefault: true,
    migrations: {
      directory: path.join(__dirname, 'migrations')
    },
    seeds: {
      directory: path.join(__dirname, 'seeds')
    }
  },
  production: {
    client: 'sqlite3', // Cambiar a 'pg' para PostgreSQL en producción
    connection: {
      filename: path.join(__dirname, 'control_jornada.db')
      // Para PostgreSQL:
      // host: process.env.DB_HOST || 'localhost',
      // port: process.env.DB_PORT || 5432,
      // database: process.env.DB_NAME || 'control_jornada',
      // user: process.env.DB_USER || 'postgres',
      // password: process.env.DB_PASSWORD || ''
    },
    useNullAsDefault: true,
    migrations: {
      directory: path.join(__dirname, 'migrations')
    }
  }
};

module.exports = config;
