import React, { useState, useEffect } from 'react';
import { Event, EventTypeConfigItem, ShiftTypeConfigItem } from '../types';

interface EventModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (event: Omit<Event, 'id'> | Event) => void;
  onDelete: (eventId: string) => void;
  event: Event | null;
  date: Date | null;
  eventTypes: EventTypeConfigItem[];
  shiftTypes: ShiftTypeConfigItem[];
  eventTypesMap: Record<string, EventTypeConfigItem>;
  shiftTypesMap: Record<string, ShiftTypeConfigItem>;
}

const EventModal: React.FC<EventModalProps> = ({ isOpen, onClose, onSave, onDelete, event, date, eventTypes, shiftTypes, eventTypesMap, shiftTypesMap }) => {
  const [formData, setFormData] = useState({
    typeId: eventTypes[0]?.id || '',
    shiftId: '',
    startTime: '',
    endTime: '',
    description: '',
    breakTime: '00:00',
  });
  
  const [dateString, setDateString] = useState('');

  // Effect to initialize or reset form state when modal opens
  useEffect(() => {
    if (isOpen) {
      if (event) {
        setFormData({
          typeId: event.typeId,
          shiftId: event.shiftId || '',
          startTime: event.startTime || '',
          endTime: event.endTime || '',
          description: event.description || '',
          breakTime: event.breakTime || '00:00',
        });
        setDateString(event.date);
      } else if (date) {
        const initialTypeId = eventTypes[0]?.id || '';
        const selectedEventType = eventTypesMap[initialTypeId];
        
        let initialState = {
          typeId: initialTypeId,
          shiftId: '',
          startTime: '',
          endTime: '',
          description: '',
          breakTime: '00:00',
        };

        if (selectedEventType) {
          const newShiftId = selectedEventType.defaultShiftId || '';
          const shiftConfig = shiftTypesMap[newShiftId];
          
          if (shiftConfig && typeof shiftConfig.time === 'string' && shiftConfig.time.includes('-')) {
            const [startTime, endTime] = shiftConfig.time.split('-');
            initialState = {
              ...initialState,
              shiftId: newShiftId,
              startTime: startTime || '',
              endTime: endTime || '',
              breakTime: shiftConfig.breakTime || '00:00',
            };
          } else {
            initialState = {
              ...initialState,
              startTime: selectedEventType.defaultStartTime || '',
              endTime: selectedEventType.defaultEndTime || '',
              breakTime: selectedEventType.breakTime || '00:00',
            };
          }
        }
        
        setFormData(initialState);
        setDateString(new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate())).toISOString().split('T')[0]);
      }
    }
  }, [event, date, isOpen, eventTypes, eventTypesMap, shiftTypesMap]);


  if (!isOpen) return null;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    setFormData(prev => {
      // Start with the basic change from user input
      const newBaseState = { ...prev, [name]: value };

      // --- Handle cascading updates based on the change ---

      // A) If EVENT TYPE changed, apply its defaults, potentially overriding shift and times.
      if (name === 'typeId') {
        const eventType = eventTypesMap[value];
        if (eventType) {
          const defaultShiftId = eventType.defaultShiftId || '';
          const shiftConfig = shiftTypesMap[defaultShiftId];
          
          if (shiftConfig && typeof shiftConfig.time === 'string' && shiftConfig.time.includes('-')) {
            // Case 1: The new event type has a valid default shift. Apply its data.
            const [startTime, endTime] = shiftConfig.time.split('-');
            return {
              ...newBaseState,
              shiftId: defaultShiftId,
              startTime: startTime || '',
              endTime: endTime || '',
              breakTime: shiftConfig.breakTime || '00:00',
            };
          } else {
            // Case 2: No valid default shift. Apply the event type's own default times.
            return {
              ...newBaseState,
              shiftId: '', // Clear shift as there's no default
              startTime: eventType.defaultStartTime || '',
              endTime: eventType.defaultEndTime || '',
              breakTime: eventType.breakTime || '00:00',
            };
          }
        }
      }

      // B) If SHIFT changed, apply its times. This overrides existing times.
      if (name === 'shiftId') {
        const shiftConfig = shiftTypesMap[value];
        if (shiftConfig && typeof shiftConfig.time === 'string' && shiftConfig.time.includes('-')) {
          // Case 1: A valid shift was selected.
          const [startTime, endTime] = shiftConfig.time.split('-');
          return {
            ...newBaseState,
            startTime: startTime || '',
            endTime: endTime || '',
            breakTime: shiftConfig.breakTime || '00:00',
          };
        } else if (!value) {
          // Case 2: "Personalizado / Sin turno" was selected. Clear times for manual input.
          const eventType = eventTypesMap[newBaseState.typeId];
          return {
            ...newBaseState,
            startTime: '',
            endTime: '',
            breakTime: eventType?.breakTime || '00:00', // Reset break time to event default
          };
        }
      }

      // C) If no special side-effects, just return the basic state update.
      return newBaseState;
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const eventType = eventTypesMap[formData.typeId];
    if (!eventType) return;

    let generatedTitle = eventType.label;
    if (formData.shiftId) {
        generatedTitle = shiftTypesMap[formData.shiftId]?.label || eventType.label;
    } else if (formData.startTime && formData.endTime) {
        generatedTitle = `${eventType.label} (${formData.startTime}-${formData.endTime})`;
    }

    const eventPayload = {
      date: dateString,
      typeId: formData.typeId,
      shiftId: formData.shiftId || undefined,
      startTime: formData.startTime || undefined,
      endTime: formData.endTime || undefined,
      description: formData.description,
      breakTime: formData.breakTime || undefined,
      title: generatedTitle,
    };

    if (event) {
      onSave({ ...event, ...eventPayload });
    } else {
      onSave(eventPayload);
    }
  };

  const handleDelete = () => {
    if (event?.id && window.confirm('¿Estás seguro de que quieres eliminar este evento?')) {
      onDelete(event.id);
    }
  };

  const selectedEventType = eventTypesMap[formData.typeId];
  const showShiftSelector = selectedEventType?.category === 'Trabajo' || selectedEventType?.category === 'Formación';
  // Always allow time selection to support partial-day events like half-day vacations.
  const showTimeSelectors = true;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-lg p-6 relative animate-fade-in-up">
        <button onClick={onClose} className="absolute top-3 right-3 text-gray-500 hover:text-gray-800">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
        </button>
        <h2 className="text-2xl font-bold mb-4 text-gray-800">{event ? 'Editar Evento' : 'Añadir Evento'}</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="typeId" className="block text-sm font-medium text-gray-700">Tipo de Evento</label>
            <select name="typeId" id="typeId" value={formData.typeId} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
              {eventTypes.map(type => (
                <option key={type.id} value={type.id}>{type.label}</option>
              ))}
            </select>
          </div>
          
          {showShiftSelector && (
             <div>
                <label htmlFor="shiftId" className="block text-sm font-medium text-gray-700">Turno</label>
                <select name="shiftId" id="shiftId" value={formData.shiftId} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                  <option value="">Personalizado / Sin turno</option>
                   {shiftTypes.map(shift => (
                    <option key={shift.id} value={shift.id}>{shift.label}</option>
                  ))}
                </select>
              </div>
          )}

          {showTimeSelectors && (
            <div className="grid grid-cols-3 gap-4">
              <div>
                <label htmlFor="startTime" className="block text-sm font-medium text-gray-700">Hora Inicio</label>
                <input type="time" name="startTime" id="startTime" value={formData.startTime} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"/>
              </div>
              <div>
                <label htmlFor="endTime" className="block text-sm font-medium text-gray-700">Hora Fin</label>
                <input type="time" name="endTime" id="endTime" value={formData.endTime} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"/>
              </div>
              <div>
                <label htmlFor="breakTime" className="block text-sm font-medium text-gray-700">Descanso</label>
                <input type="text" pattern="[0-9]{2}:[0-9]{2}" name="breakTime" id="breakTime" value={formData.breakTime} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="HH:mm"/>
              </div>
            </div>
          )}

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">Descripción (Opcional)</label>
            <textarea name="description" id="description" value={formData.description} onChange={handleChange} rows={3} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"></textarea>
          </div>
          <div className="pt-4 flex justify-between items-center">
            <div>
              {event && (
                  <button type="button" onClick={handleDelete} aria-label="Eliminar evento" className="rounded-md border border-red-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-red-700 hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                      Eliminar
                  </button>
              )}
            </div>
            <div className="flex justify-end space-x-3">
              <button type="button" onClick={onClose} className="rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Cancelar
              </button>
              <button type="submit" className="rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Guardar
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EventModal;