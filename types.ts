// FIX: Removed self-import of EventTypeConfigItem to resolve a name collision.

export enum EventCategory {
  Trabajo = 'Trabajo',
  Vacaciones = 'Vacaciones',
  Permiso = 'Permiso',
  Ausencia = 'Ausencia',
  Formacion = 'Formación',
}

export enum ComputationType {
  Adds = 'adds',
  Subtracts = 'subtracts',
  Neutral = 'neutral',
}

export interface EventTypeConfigItem {
  id: string;
  label: string;
  category: EventCategory;
  computationType: ComputationType;
  color: string;
  defaultStartTime?: string; // HH:mm
  defaultEndTime?: string; // HH:mm
  breakTime: string; // HH:mm
  defaultShiftId?: string;
  isDeletable: boolean;
}

export interface ShiftTypeConfigItem {
  id: string;
  label: string;
  icon: string;
  time: string; // 'HH:mm-HH:mm'
  hours: number;
  isDeletable: boolean;
  breakTime: string; // HH:mm
}

export interface Event {
  id: string;
  date: string; // YYYY-MM-DD
  typeId: string;
  title?: string;
  shiftId?: string;
  startTime?: string; // HH:mm
  endTime?: string; // HH:mm
  description?: string;
  breakTime?: string; // HH:mm, allows override
}

export type TheoreticalCalendar = Record<string, string>; // YYYY-MM-DD -> shiftId

// UTILITY FUNCTIONS

export const parseTimeToMinutes = (time: string): number => {
  if (!time || !time.includes(':')) return 0;
  const [hours, minutes] = time.split(':').map(Number);
  if (isNaN(hours) || isNaN(minutes)) return 0;
  return hours * 60 + minutes;
};


export const calculateEventNetHours = (event: Event, eventType: EventTypeConfigItem): number => {
    if (!eventType) return 0;

    let netHours = 0;

    if (event.startTime && event.endTime) {
        const start = parseTimeToMinutes(event.startTime);
        let end = parseTimeToMinutes(event.endTime);
        if (end < start) end += 24 * 60; // overnight shift
        const duration = (end - start) / 60;
        
        const breakTime = event.breakTime || '00:00';
        const breakHours = parseTimeToMinutes(breakTime) / 60;
        
        netHours = Math.max(0, duration - breakHours);
    }

    if (eventType.computationType === ComputationType.Subtracts) {
        return -netHours;
    }
    if (eventType.computationType === ComputationType.Neutral) {
        return 0;
    }
    // Default to 'adds'
    return netHours;
};