import express from 'express';
import { body, validationResult, param, query } from 'express-validator';
import { db } from '../database/db.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// Get theoretical calendar for a specific year
router.get('/:year', [
  param('year').isInt({ min: 2020, max: 2030 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const year = parseInt(req.params.year);

    const calendarEntries = await db('theoretical_calendar')
      .where('user_id', req.user.id)
      .whereRaw('strftime("%Y", date) = ?', [year.toString()])
      .select('date', 'shift_id as shiftId')
      .orderBy('date');

    // Convert to the expected format: { "YYYY-MM-DD": "shiftId" }
    const calendar = {};
    calendarEntries.forEach(entry => {
      calendar[entry.date] = entry.shiftId;
    });

    res.json(calendar);

  } catch (error) {
    console.error('Get theoretical calendar error:', error);
    res.status(500).json({ error: 'Failed to fetch theoretical calendar' });
  }
});

// Get theoretical calendar entries with date range filter
router.get('/', [
  query('startDate').optional().isISO8601(),
  query('endDate').optional().isISO8601(),
  query('year').optional().isInt({ min: 2020, max: 2030 }),
  query('month').optional().isInt({ min: 1, max: 12 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    let query = db('theoretical_calendar')
      .where('user_id', req.user.id);

    // Apply filters
    if (req.query.year) {
      query = query.whereRaw('strftime("%Y", date) = ?', [req.query.year]);
    }
    
    if (req.query.month && req.query.year) {
      query = query.whereRaw('strftime("%Y-%m", date) = ?', [
        `${req.query.year}-${String(req.query.month).padStart(2, '0')}`
      ]);
    }

    if (req.query.startDate) {
      query = query.where('date', '>=', req.query.startDate);
    }

    if (req.query.endDate) {
      query = query.where('date', '<=', req.query.endDate);
    }

    const calendarEntries = await query
      .select('date', 'shift_id as shiftId', 'created_at as createdAt', 'updated_at as updatedAt')
      .orderBy('date');

    // Convert to the expected format: { "YYYY-MM-DD": "shiftId" }
    const calendar = {};
    calendarEntries.forEach(entry => {
      calendar[entry.date] = entry.shiftId;
    });

    res.json(calendar);

  } catch (error) {
    console.error('Get theoretical calendar error:', error);
    res.status(500).json({ error: 'Failed to fetch theoretical calendar' });
  }
});

// Set/update a single day in theoretical calendar
router.put('/day', [
  body('date').isISO8601().toDate(),
  body('shiftId').isLength({ min: 1, max: 50 }).trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { date, shiftId } = req.body;
    const dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD format

    // Verify shift type belongs to user
    const shiftType = await db('shift_types')
      .where('id', shiftId)
      .where('user_id', req.user.id)
      .first();

    if (!shiftType) {
      return res.status(400).json({ error: 'Invalid shift type' });
    }

    // Check if entry already exists
    const existingEntry = await db('theoretical_calendar')
      .where('user_id', req.user.id)
      .where('date', dateStr)
      .first();

    if (existingEntry) {
      // Update existing entry
      await db('theoretical_calendar')
        .where('user_id', req.user.id)
        .where('date', dateStr)
        .update({ shift_id: shiftId });
    } else {
      // Create new entry
      await db('theoretical_calendar').insert({
        user_id: req.user.id,
        date: dateStr,
        shift_id: shiftId
      });
    }

    res.json({ 
      message: 'Theoretical calendar updated successfully',
      date: dateStr,
      shiftId
    });

  } catch (error) {
    console.error('Update theoretical calendar day error:', error);
    res.status(500).json({ error: 'Failed to update theoretical calendar' });
  }
});

// Remove a day from theoretical calendar
router.delete('/day/:date', [
  param('date').isISO8601()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const dateStr = req.params.date;

    const deletedCount = await db('theoretical_calendar')
      .where('user_id', req.user.id)
      .where('date', dateStr)
      .del();

    if (deletedCount === 0) {
      return res.status(404).json({ error: 'Calendar entry not found' });
    }

    res.json({ 
      message: 'Calendar entry removed successfully',
      date: dateStr
    });

  } catch (error) {
    console.error('Delete theoretical calendar day error:', error);
    res.status(500).json({ error: 'Failed to remove calendar entry' });
  }
});

// Bulk update theoretical calendar (for month planning)
router.put('/bulk', [
  body('entries').isArray(),
  body('entries.*.date').isISO8601(),
  body('entries.*.shiftId').optional().isLength({ min: 1, max: 50 }).trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { entries } = req.body;

    // Validate all shift types belong to user
    const shiftIds = [...new Set(entries.filter(e => e.shiftId).map(e => e.shiftId))];
    if (shiftIds.length > 0) {
      const validShifts = await db('shift_types')
        .where('user_id', req.user.id)
        .whereIn('id', shiftIds)
        .select('id');

      const validShiftIds = validShifts.map(s => s.id);
      const invalidShifts = shiftIds.filter(id => !validShiftIds.includes(id));

      if (invalidShifts.length > 0) {
        return res.status(400).json({ 
          error: 'Invalid shift types',
          invalidShifts
        });
      }
    }

    // Process entries in transaction
    await db.transaction(async (trx) => {
      for (const entry of entries) {
        const dateStr = new Date(entry.date).toISOString().split('T')[0];

        if (entry.shiftId) {
          // Upsert entry
          const existingEntry = await trx('theoretical_calendar')
            .where('user_id', req.user.id)
            .where('date', dateStr)
            .first();

          if (existingEntry) {
            await trx('theoretical_calendar')
              .where('user_id', req.user.id)
              .where('date', dateStr)
              .update({ shift_id: entry.shiftId });
          } else {
            await trx('theoretical_calendar').insert({
              user_id: req.user.id,
              date: dateStr,
              shift_id: entry.shiftId
            });
          }
        } else {
          // Remove entry if shiftId is null/empty
          await trx('theoretical_calendar')
            .where('user_id', req.user.id)
            .where('date', dateStr)
            .del();
        }
      }
    });

    res.json({ 
      message: 'Theoretical calendar bulk updated successfully',
      entriesProcessed: entries.length
    });

  } catch (error) {
    console.error('Bulk update theoretical calendar error:', error);
    res.status(500).json({ error: 'Failed to bulk update theoretical calendar' });
  }
});

// Clear theoretical calendar for a specific year
router.delete('/year/:year', [
  param('year').isInt({ min: 2020, max: 2030 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const year = parseInt(req.params.year);

    const deletedCount = await db('theoretical_calendar')
      .where('user_id', req.user.id)
      .whereRaw('strftime("%Y", date) = ?', [year.toString()])
      .del();

    res.json({ 
      message: `Theoretical calendar cleared for year ${year}`,
      entriesDeleted: deletedCount
    });

  } catch (error) {
    console.error('Clear theoretical calendar year error:', error);
    res.status(500).json({ error: 'Failed to clear theoretical calendar' });
  }
});

export default router;
