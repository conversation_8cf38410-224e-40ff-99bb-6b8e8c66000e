import React from 'react';
import type { Event, EventTypeConfigItem, ShiftTypeConfigItem } from '../types';
import { DAY_NAMES_SHORT } from '../constants';
import type { TheoreticalCalendar } from '../services/theoreticalCalendarService';

interface CalendarProps {
  currentDate: Date;
  events: Event[];
  onDayClick: (date: Date) => void;
  onEventClick: (event: Event) => void;
  isPlanningMode: boolean;
  theoreticalCalendar: TheoreticalCalendar;
  eventTypesMap: Record<string, EventTypeConfigItem>;
  shiftTypesMap: Record<string, ShiftTypeConfigItem>;
}

const getWeekNumber = (d: Date): number => {
    d = new Date(Date.UTC(d.getFullYear(), d.getMonth(), d.getDate()));
    d.setUTCDate(d.getUTCDate() + 4 - (d.getUTCDay() || 7));
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    const weekNo = Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
    return weekNo;
};


const Calendar: React.FC<CalendarProps> = ({ currentDate, events, onDayClick, onEventClick, isPlanningMode, theoreticalCalendar, eventTypesMap, shiftTypesMap }) => {
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();

  const renderCalendarGrid = () => {
    const gridCells = [];
    const firstDayOfMonth = new Date(year, month, 1).getDay();
    const startDayOffset = (firstDayOfMonth === 0) ? 6 : firstDayOfMonth - 1;
    
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const numRows = Math.ceil((startDayOffset + daysInMonth) / 7);

    let dayCounter = 1;

    for (let i = 0; i < numRows; i++) {
        const dateForWeekNumber = new Date(year, month, (i * 7) - startDayOffset + 1);
        gridCells.push(
            <div key={`weeknum-${i}`} className="flex items-center justify-center p-2 border-r border-b border-gray-200 bg-gray-50 text-sm font-semibold text-gray-500">
                {getWeekNumber(dateForWeekNumber)}
            </div>
        );
        
        for (let j = 0; j < 7; j++) {
            if (i === 0 && j < startDayOffset) {
                gridCells.push(<div key={`empty-start-${j}`} className="border-r border-b border-gray-200"></div>);
            } else if (dayCounter > daysInMonth) {
                gridCells.push(<div key={`empty-end-${i}-${j}`} className="border-r border-b border-gray-200"></div>);
            } else {
                const date = new Date(Date.UTC(year, month, dayCounter));
                const dateString = date.toISOString().split('T')[0];
                const dayEvents = events.filter(e => e.date === dateString);
                const plannedShiftId = theoreticalCalendar[dateString];
                const plannedShift = plannedShiftId ? shiftTypesMap[plannedShiftId] : null;
                const today = new Date();
                const isToday = today.getFullYear() === year && today.getMonth() === month && today.getDate() === dayCounter;

                gridCells.push(
                    <div 
                      key={dateString} 
                      className="p-2 border-r border-b border-gray-200 min-h-[120px] flex flex-col cursor-pointer hover:bg-gray-50 transition-colors relative"
                      onClick={() => onDayClick(date)}
                    >
                      <span className={`font-semibold text-sm ${isToday ? 'bg-indigo-600 text-white rounded-full w-7 h-7 flex items-center justify-center' : 'text-gray-700'}`}>
                        {dayCounter}
                      </span>
                      <div className="mt-1 flex-grow space-y-1">
                        {isPlanningMode && plannedShift && (
                           <div className="bg-gray-200 text-gray-700 text-xs font-semibold p-1 rounded-md text-center">
                             {plannedShift.icon} {plannedShift.label}
                           </div>
                        )}
                        {!isPlanningMode && dayEvents.map(event => {
                          const eventType = eventTypesMap[event.typeId];
                          const bgColor = eventType ? `bg-${eventType.color}` : 'bg-gray-400';
                          const displayTitle = event.description ? `${event.title}: ${event.description}` : event.title;
                          return (
                            <div 
                              key={event.id}
                              onClick={(e) => { e.stopPropagation(); onEventClick(event); }}
                              title={displayTitle}
                              className={`${bgColor} text-white text-xs font-bold p-1 rounded-md truncate cursor-pointer hover:opacity-80`}
                            >
                              {displayTitle}
                            </div>
                          );
                        })}
                      </div>
                    </div>
                );
                dayCounter++;
            }
        }
    }
    return gridCells;
  };


  return (
    <div className="bg-white rounded-xl shadow-lg p-4">
      <div className="grid grid-cols-8 text-center font-bold text-gray-600 border-b border-gray-200">
        <div className="py-2 text-sm text-gray-500">Sem</div>
        {DAY_NAMES_SHORT.map(day => (
          <div key={day} className="py-2">{day}</div>
        ))}
      </div>
      <div className="grid grid-cols-8">
        {renderCalendarGrid()}
      </div>
    </div>
  );
};

export default Calendar;
