

import React from 'react';
import type { Event, EventTypeConfigItem, ShiftTypeConfigItem } from '../types';
import type { TheoreticalCalendar } from '../services/theoreticalCalendarService';
import * as XLSX from 'xlsx';
import jsPDF from 'jspdf';
// FIX: Use functional import for jspdf-autotable to avoid typescript issues with prototype extension.
import autoTable from 'jspdf-autotable';

interface DataExportProps {
    events: Event[];
    theoreticalCalendar: TheoreticalCalendar;
    year: number;
    eventTypesMap: Record<string, EventTypeConfigItem>;
    shiftTypesMap: Record<string, ShiftTypeConfigItem>;
}

// FIX: Removed the problematic custom interface `jsPDFWithAutoTable`.
// The functional call to autoTable doesn't require it and avoids type conflicts.

const DataExport: React.FC<DataExportProps> = ({ events, theoreticalCalendar, year, eventTypesMap, shiftTypesMap }) => {
    
    const getYearEvents = () => events.filter(e => e.date.startsWith(String(year)));
    const getYearPlanning = () => {
        const yearPlanning: TheoreticalCalendar = {};
        for(const date in theoreticalCalendar){
            if(date.startsWith(String(year))) {
                yearPlanning[date] = theoreticalCalendar[date];
            }
        }
        return yearPlanning;
    };
    
    const downloadCSV = (content: string, fileName: string) => {
        const blob = new Blob([`\uFEFF${content}`], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute("download", fileName);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const handleExportCSV = () => {
        const yearEvents = getYearEvents();
        if(yearEvents.length === 0) {
            alert(`No hay eventos reales registrados para el año ${year}.`);
            return;
        }
        const headers = ['id', 'date', 'type', 'title', 'shift', 'startTime', 'endTime', 'description'];
        const rows = yearEvents.map(e => {
            const eventType = eventTypesMap[e.typeId];
            const shiftType = e.shiftId ? shiftTypesMap[e.shiftId] : null;
            return [e.id, e.date, eventType?.label || e.typeId, `"${(e.title || '').replace(/"/g, '""')}"`, shiftType?.label || '', e.startTime || '', e.endTime || '', `"${(e.description || '').replace(/"/g, '""')}"`].join(',');
        });
        const csvContent = [headers.join(','), ...rows].join('\n');
        downloadCSV(csvContent, `eventos_reales_${year}.csv`);
    };

    const handleExportXLSX = () => {
        const yearEvents = getYearEvents();
        const yearPlanning = getYearPlanning();

        if (yearEvents.length === 0 && Object.keys(yearPlanning).length === 0) {
            alert(`No hay datos para exportar en el año ${year}.`);
            return;
        }
        
        const wb = XLSX.utils.book_new();
        
        // Sheet 1: Real Events
        const eventsHeaders = ['Fecha', 'Tipo', 'Título', 'Turno', 'Hora Inicio', 'Hora Fin', 'Descripción'];
        const eventsData = yearEvents.map(e => ({
            Fecha: e.date,
            Tipo: eventTypesMap[e.typeId]?.label || e.typeId,
            Título: e.title || '',
            Turno: e.shiftId ? shiftTypesMap[e.shiftId]?.label : '',
            'Hora Inicio': e.startTime || '',
            'Hora Fin': e.endTime || '',
            Descripción: e.description || ''
        }));
        const wsEvents = XLSX.utils.json_to_sheet([eventsHeaders, ...eventsData.map(Object.values)], { skipHeader: true });
        XLSX.utils.book_append_sheet(wb, wsEvents, "Eventos Reales");

        // Sheet 2: Theoretical Planning
        const planningHeaders = ['Fecha', 'Turno Asignado', 'Horario', 'Horas Computadas'];
        const planningData = Object.entries(yearPlanning).sort(([dateA], [dateB]) => dateA.localeCompare(dateB)).map(([date, shiftId]) => {
            const shift = shiftTypesMap[shiftId];
            return {
                Fecha: date,
                'Turno Asignado': shift?.label || 'N/A',
                Horario: shift?.time || '',
                'Horas Computadas': shift?.hours || 0
            }
        });
        const wsPlanning = XLSX.utils.json_to_sheet([planningHeaders, ...planningData.map(Object.values)], { skipHeader: true });
        XLSX.utils.book_append_sheet(wb, wsPlanning, "Planificación Teórica");

        XLSX.writeFile(wb, `control_jornada_${year}.xlsx`);
    };

    const handleExportPDF = () => {
        const yearEvents = getYearEvents();
        const yearPlanning = getYearPlanning();

        if (yearEvents.length === 0 && Object.keys(yearPlanning).length === 0) {
            alert(`No hay datos para exportar en el año ${year}.`);
            return;
        }

        // FIX: Instantiate jsPDF directly without casting. All methods are now correctly typed.
        const doc = new jsPDF();
        
        doc.text(`Informe de Jornada Laboral - Año ${year}`, 14, 16);
        doc.setFontSize(10);
        doc.text(`Generado el: ${new Date().toLocaleDateString('es-ES')}`, 14, 22);

        if(yearEvents.length > 0) {
            // FIX: Use functional call to autoTable.
            autoTable(doc, {
                startY: 30,
                head: [['Fecha', 'Tipo', 'Título', 'Turno', 'Horario']],
                body: yearEvents.map(e => [
                    e.date,
                    eventTypesMap[e.typeId]?.label || e.typeId,
                    e.title || '',
                    e.shiftId ? shiftTypesMap[e.shiftId]?.label : '',
                    e.startTime ? `${e.startTime} - ${e.endTime}`: ''
                ]),
                headStyles: { fillColor: [41, 128, 185] },
                didDrawPage: (data) => {
                    doc.setFontSize(18);
                    doc.text('Eventos Reales', 14, data.settings.margin.top);
                }
            });
        }
        
        if(Object.keys(yearPlanning).length > 0) {
            const planningData = Object.entries(yearPlanning).sort(([dateA], [dateB]) => dateA.localeCompare(dateB)).map(([date, shiftId]) => {
                const shift = shiftTypesMap[shiftId];
                return [date, shift?.label || 'N/A', shift?.time || '', shift?.hours.toFixed(2) || 0];
            });

            // FIX: Safely access lastAutoTable property (added by the plugin) and correct page height check.
            const lastTable = (doc as any).lastAutoTable;
            const startY = lastTable ? lastTable.finalY + 15 : 30;

            // FIX: Use functional call to autoTable.
            autoTable(doc, {
                // Check if we need a new page
                startY: startY > doc.internal.pageSize.height ? 30 : startY,
                head: [['Fecha', 'Turno Asignado', 'Horario', 'Horas']],
                body: planningData,
                headStyles: { fillColor: [39, 174, 96] },
                didDrawPage: (data) => {
                    doc.setFontSize(18);
                    doc.text('Planificación Teórica', 14, data.settings.margin.top);
                }
            });
        }

        doc.save(`informe_jornada_${year}.pdf`);
    };

    return (
        <div className="bg-white rounded-xl shadow-lg p-6 mt-8">
            <h3 className="text-xl font-bold text-gray-700 mb-4">Exportar Datos ({year})</h3>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                <button onClick={handleExportCSV} className="w-full bg-gray-600 text-white font-bold py-2 px-3 rounded-lg hover:bg-gray-700 transition duration-300 text-sm">
                    CSV
                </button>
                <button onClick={handleExportXLSX} className="w-full bg-green-600 text-white font-bold py-2 px-3 rounded-lg hover:bg-green-700 transition duration-300 text-sm">
                    XLSX
                </button>
                <button onClick={handleExportPDF} className="w-full bg-red-600 text-white font-bold py-2 px-3 rounded-lg hover:bg-red-700 transition duration-300 text-sm">
                    PDF
                </button>
            </div>
        </div>
    );
};

export default DataExport;
