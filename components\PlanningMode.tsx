import React, { useState } from 'react';
import type { ShiftTypeConfigItem } from '../types';

interface PlanningModeProps {
  isPlanningMode: boolean;
  onTogglePlanningMode: (isActive: boolean) => void;
  onBulkUpdate: (shiftId: string, days: string) => void;
  shiftTypes: ShiftTypeConfigItem[];
}

const DAY_OPTIONS: Record<string, string> = {
    weekdays: 'días laborables',
    weekends: 'fines de semana',
    all: 'todos los días',
    '1': 'Lunes',
    '2': 'Martes',
    '3': 'Miércoles',
    '4': 'Jueves',
    '5': 'Viernes',
    '6': 'Sábado',
    '0': 'Domingo',
};

const PlanningMode: React.FC<PlanningModeProps> = ({ isPlanningMode, onTogglePlanningMode, onBulkUpdate, shiftTypes }) => {
  const [selectedShiftId, setSelectedShiftId] = useState<string>(shiftTypes[0]?.id || '');
  const [selectedDays, setSelectedDays] = useState<string>('weekdays');

  const handleApplyBulk = () => {
    const shiftLabel = shiftTypes.find(s => s.id === selectedShiftId)?.label || '';
    const daysLabel = DAY_OPTIONS[selectedDays];
    if (confirm(`¿Quieres asignar el turno '${shiftLabel}' a todos los '${daysLabel}' de este mes?`)) {
        onBulkUpdate(selectedShiftId, selectedDays);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-bold text-gray-700">Modo Planificación</h3>
        <label htmlFor="planning-toggle" className="flex items-center cursor-pointer">
          <div className="relative">
            <input 
                id="planning-toggle" 
                type="checkbox" 
                className="sr-only" 
                checked={isPlanningMode}
                onChange={(e) => onTogglePlanningMode(e.target.checked)} 
            />
            <div className="block bg-gray-200 w-14 h-8 rounded-full"></div>
            <div className={`dot absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition-transform ${isPlanningMode ? 'transform translate-x-6 bg-indigo-600' : ''}`}></div>
          </div>
        </label>
      </div>
       <p className="text-sm text-gray-500 mb-4">
        Activa este modo para definir tu calendario teórico anual. Haz clic en un día para asignar un turno.
      </p>

      {isPlanningMode && shiftTypes.length > 0 && (
        <div className="bg-gray-50 p-4 rounded-lg space-y-4 animate-fade-in-up">
          <h4 className="font-semibold text-gray-600">Asignación Rápida Mensual</h4>
          <div>
            <label htmlFor="bulk-shift" className="block text-sm font-medium text-gray-700">Turno a asignar</label>
            <select
                id="bulk-shift"
                value={selectedShiftId}
                onChange={(e) => setSelectedShiftId(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
                {shiftTypes.map(shift => (
                  <option key={shift.id} value={shift.id}>{shift.label}</option>
                ))}
            </select>
          </div>
           <div>
            <label htmlFor="bulk-days" className="block text-sm font-medium text-gray-700">Aplicar a</label>
            <select
                id="bulk-days"
                value={selectedDays}
                onChange={(e) => setSelectedDays(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
                <option value="weekdays">Días laborables</option>
                <option value="weekends">Fines de semana</option>
                <option value="all">Todos los días</option>
                <option disabled>---</option>
                <option value="1">Lunes</option>
                <option value="2">Martes</option>
                <option value="3">Miércoles</option>
                <option value="4">Jueves</option>
                <option value="5">Viernes</option>
                <option value="6">Sábado</option>
                <option value="0">Domingo</option>
            </select>
          </div>
          <button
            onClick={handleApplyBulk}
            className="w-full bg-indigo-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-indigo-700 transition duration-300"
            >
            Aplicar a este Mes
          </button>
        </div>
      )}
    </div>
  );
};

export default PlanningMode;