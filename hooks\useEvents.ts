
import { useState, useEffect, useCallback } from 'react';
import type { Event } from '../types';
import * as eventService from '../services/eventService';

export const useEvents = () => {
  const [events, setEvents] = useState<Event[]>([]);

  useEffect(() => {
    setEvents(eventService.getEvents());
  }, []);

  const addEvent = useCallback((eventData: Omit<Event, 'id'>) => {
    const newEvent = eventService.addEvent(eventData);
    setEvents(prevEvents => [...prevEvents, newEvent]);
  }, []);

  const updateEvent = useCallback((updatedEvent: Event) => {
    eventService.updateEvent(updatedEvent);
    setEvents(prevEvents => 
      prevEvents.map(e => (e.id === updatedEvent.id ? updatedEvent : e))
    );
  }, []);

  const deleteEvent = useCallback((eventId: string) => {
    eventService.deleteEvent(eventId);
    setEvents(prevEvents => prevEvents.filter(e => e.id !== eventId));
  }, []);

  return { events, addEvent, updateEvent, deleteEvent };
};
