
import { useState, useEffect, useCallback } from 'react';
import type { Event } from '../types';
import * as eventService from '../services/eventService';

export const useEvents = () => {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadEvents = async () => {
      try {
        setLoading(true);
        setError(null);
        const loadedEvents = await eventService.getEvents();
        setEvents(loadedEvents);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Error loading events');
        console.error('Error loading events:', err);
      } finally {
        setLoading(false);
      }
    };

    loadEvents();
  }, []);

  const addEvent = useCallback(async (eventData: Omit<Event, 'id'>) => {
    try {
      const newEvent = await eventService.addEvent(eventData);
      setEvents(prevEvents => [...prevEvents, newEvent]);
      return newEvent;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error adding event';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const updateEvent = useCallback(async (updatedEvent: Event) => {
    try {
      await eventService.updateEvent(updatedEvent);
      setEvents(prevEvents =>
        prevEvents.map(e => (e.id === updatedEvent.id ? updatedEvent : e))
      );
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error updating event';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const deleteEvent = useCallback(async (eventId: string) => {
    try {
      await eventService.deleteEvent(eventId);
      setEvents(prevEvents => prevEvents.filter(e => e.id !== eventId));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error deleting event';
      setError(errorMessage);
      throw err;
    }
  }, []);

  return { events, addEvent, updateEvent, deleteEvent, loading, error };
};
