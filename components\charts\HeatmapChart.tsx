import React from 'react';

interface HeatmapData {
  x: string; // Category or time period
  y: string; // Event type or day
  value: number;
  displayValue?: string;
}

interface HeatmapChartProps {
  title: string;
  data: HeatmapData[];
  xLabels: string[];
  yLabels: string[];
  colorScheme?: 'blue' | 'green' | 'red' | 'purple';
}

const COLOR_SCHEMES = {
  blue: {
    low: '#dbeafe',
    medium: '#93c5fd',
    high: '#3b82f6',
    highest: '#1d4ed8'
  },
  green: {
    low: '#dcfce7',
    medium: '#86efac',
    high: '#22c55e',
    highest: '#15803d'
  },
  red: {
    low: '#fee2e2',
    medium: '#fca5a5',
    high: '#ef4444',
    highest: '#dc2626'
  },
  purple: {
    low: '#f3e8ff',
    medium: '#c4b5fd',
    high: '#a855f7',
    highest: '#7c3aed'
  }
};

const HeatmapChart: React.FC<HeatmapChartProps> = ({ 
  title, 
  data, 
  xLabels, 
  yLabels, 
  colorScheme = 'blue' 
}) => {
  const maxValue = Math.max(...data.map(d => d.value));
  const minValue = Math.min(...data.map(d => d.value));
  const range = maxValue - minValue;

  const getColor = (value: number) => {
    if (range === 0) return COLOR_SCHEMES[colorScheme].low;
    
    const normalized = (value - minValue) / range;
    const colors = COLOR_SCHEMES[colorScheme];
    
    if (normalized <= 0.25) return colors.low;
    if (normalized <= 0.5) return colors.medium;
    if (normalized <= 0.75) return colors.high;
    return colors.highest;
  };

  const getIntensity = (value: number) => {
    if (range === 0) return 0.1;
    return Math.max(0.1, (value - minValue) / range);
  };

  // Create a map for quick lookup
  const dataMap = new Map<string, HeatmapData>();
  data.forEach(item => {
    dataMap.set(`${item.x}-${item.y}`, item);
  });

  return (
    <div className="h-full flex flex-col">
      <h3 className="text-lg font-bold text-gray-700 mb-4 text-center">{title}</h3>
      
      <div className="flex-grow flex flex-col overflow-hidden">
        {/* Heatmap Grid */}
        <div className="flex-grow flex">
          {/* Y-axis labels */}
          <div className="flex flex-col justify-between text-xs text-gray-600 pr-2 py-1">
            {yLabels.map((label, index) => (
              <div key={index} className="flex items-center h-8 text-right">
                <span className="truncate max-w-20" title={label}>
                  {label.length > 10 ? label.substring(0, 10) + '...' : label}
                </span>
              </div>
            ))}
          </div>
          
          {/* Grid */}
          <div className="flex-grow">
            {/* X-axis labels */}
            <div className="flex mb-1">
              {xLabels.map((label, index) => (
                <div key={index} className="flex-1 text-center text-xs text-gray-600 px-1">
                  <span className="transform -rotate-45 inline-block origin-center">
                    {label.length > 6 ? label.substring(0, 6) + '...' : label}
                  </span>
                </div>
              ))}
            </div>
            
            {/* Heatmap cells */}
            <div className="grid gap-1" style={{ gridTemplateRows: `repeat(${yLabels.length}, 1fr)` }}>
              {yLabels.map((yLabel, yIndex) => (
                <div key={yIndex} className="flex gap-1">
                  {xLabels.map((xLabel, xIndex) => {
                    const cellData = dataMap.get(`${xLabel}-${yLabel}`);
                    const value = cellData?.value || 0;
                    const displayValue = cellData?.displayValue || value.toString();
                    
                    return (
                      <div
                        key={xIndex}
                        className="flex-1 h-8 rounded border border-gray-200 flex items-center justify-center text-xs font-medium relative group cursor-pointer transition-all duration-200 hover:scale-105"
                        style={{
                          backgroundColor: getColor(value),
                          color: getIntensity(value) > 0.5 ? 'white' : '#374151'
                        }}
                      >
                        <span className="truncate px-1">
                          {value > 0 ? displayValue : ''}
                        </span>
                        
                        {/* Tooltip */}
                        {value > 0 && (
                          <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block bg-gray-800 text-white text-xs rounded py-1 px-2 pointer-events-none whitespace-nowrap z-10">
                            <div className="font-semibold">{xLabel} - {yLabel}</div>
                            <div>Valor: {displayValue}</div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* Legend */}
        <div className="mt-4 flex items-center justify-center space-x-4 text-xs text-gray-600">
          <span>Menos</span>
          <div className="flex space-x-1">
            {Object.values(COLOR_SCHEMES[colorScheme]).map((color, index) => (
              <div
                key={index}
                className="w-4 h-4 rounded"
                style={{ backgroundColor: color }}
              ></div>
            ))}
          </div>
          <span>Más</span>
          {maxValue > 0 && (
            <span className="ml-4 text-gray-500">
              Rango: {minValue} - {maxValue}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default HeatmapChart;
