import type { Event } from '../types';
import { eventsApi, getAuthToken } from './apiService';

// Fallback to localStorage if not authenticated
const STORAGE_KEY = 'workCalendarEvents';

const getEventsFromLocalStorage = (): Event[] => {
  try {
    const eventsJson = localStorage.getItem(STORAGE_KEY);
    const parsedEvents = eventsJson ? JSON.parse(eventsJson) : [];
    // Basic data migration/validation check
    if (Array.isArray(parsedEvents) && parsedEvents.every(e => e.id && e.date && (e.typeId || e.type))) {
      // Simple migration from old 'type' field to 'typeId'
      return parsedEvents.map(e => {
        if (e.type && !e.typeId) {
          e.typeId = e.type.toLowerCase();
          delete e.type;
        }
        if (e.shift && !e.shiftId) {
          e.shiftId = e.shift.toLowerCase();
          delete e.shift;
        }
        return e;
      });
    }
    return [];
  } catch (error) {
    console.error("Error al leer eventos de localStorage:", error);
    localStorage.removeItem(STORAGE_KEY); // Clear corrupted data
    return [];
  }
};

export const getEvents = async (): Promise<Event[]> => {
  try {
    if (getAuthToken()) {
      // Use API if authenticated
      return await eventsApi.getAll();
    } else {
      // Fallback to localStorage
      return getEventsFromLocalStorage();
    }
  } catch (error) {
    console.error("Error al obtener eventos:", error);
    // Fallback to localStorage on API error
    return getEventsFromLocalStorage();
  }
};

const saveEventsToLocalStorage = (events: Event[]): void => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(events));
  } catch (error) {
    console.error("Error al guardar eventos en localStorage:", error);
  }
};

export const addEvent = async (event: Omit<Event, 'id'>): Promise<Event> => {
  try {
    if (getAuthToken()) {
      // Use API if authenticated
      return await eventsApi.create(event);
    } else {
      // Fallback to localStorage
      const events = await getEvents();
      const newEvent: Event = {
        ...event,
        id: crypto.randomUUID(),
      };
      const updatedEvents = [...events, newEvent];
      saveEventsToLocalStorage(updatedEvents);
      return newEvent;
    }
  } catch (error) {
    console.error("Error al crear evento:", error);
    throw error;
  }
};

export const updateEvent = async (updatedEvent: Event): Promise<Event | undefined> => {
  try {
    if (getAuthToken()) {
      // Use API if authenticated
      return await eventsApi.update(updatedEvent.id, updatedEvent);
    } else {
      // Fallback to localStorage
      const events = await getEvents();
      const eventIndex = events.findIndex(e => e.id === updatedEvent.id);
      if (eventIndex > -1) {
        events[eventIndex] = updatedEvent;
        saveEventsToLocalStorage(events);
        return updatedEvent;
      }
      return undefined;
    }
  } catch (error) {
    console.error("Error al actualizar evento:", error);
    throw error;
  }
};

export const deleteEvent = async (eventId: string): Promise<void> => {
  try {
    if (getAuthToken()) {
      // Use API if authenticated
      await eventsApi.delete(eventId);
    } else {
      // Fallback to localStorage
      const events = await getEvents();
      const updatedEvents = events.filter(e => e.id !== eventId);
      saveEventsToLocalStorage(updatedEvents);
    }
  } catch (error) {
    console.error("Error al eliminar evento:", error);
    throw error;
  }
};
