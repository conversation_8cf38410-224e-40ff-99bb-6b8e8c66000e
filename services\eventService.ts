import type { Event } from '../types';

const STORAGE_KEY = 'workCalendarEvents';

export const getEvents = (): Event[] => {
  try {
    const eventsJson = localStorage.getItem(STORAGE_KEY);
    const parsedEvents = eventsJson ? JSON.parse(eventsJson) : [];
    // Basic data migration/validation check
    if (Array.isArray(parsedEvents) && parsedEvents.every(e => e.id && e.date && (e.typeId || e.type))) {
      // Simple migration from old 'type' field to 'typeId'
      return parsedEvents.map(e => {
        if (e.type && !e.typeId) {
          e.typeId = e.type.toLowerCase();
          delete e.type;
        }
        if (e.shift && !e.shiftId) {
          e.shiftId = e.shift.toLowerCase();
          delete e.shift;
        }
        return e;
      });
    }
    return [];
  } catch (error) {
    console.error("Error al leer eventos de localStorage:", error);
    localStorage.removeItem(STORAGE_KEY); // Clear corrupted data
    return [];
  }
};

export const saveEvents = (events: Event[]): void => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(events));
  } catch (error) {
    console.error("Error al guardar eventos en localStorage:", error);
  }
};

export const addEvent = (event: Omit<Event, 'id'>): Event => {
  const events = getEvents();
  const newEvent: Event = {
    ...event,
    id: crypto.randomUUID(),
  };
  const updatedEvents = [...events, newEvent];
  saveEvents(updatedEvents);
  return newEvent;
};

export const updateEvent = (updatedEvent: Event): Event | undefined => {
  const events = getEvents();
  const eventIndex = events.findIndex(e => e.id === updatedEvent.id);
  if (eventIndex > -1) {
    events[eventIndex] = updatedEvent;
    saveEvents(events);
    return updatedEvent;
  }
  return undefined;
};

export const deleteEvent = (eventId: string): void => {
  const events = getEvents();
  const updatedEvents = events.filter(e => e.id !== eventId);
  saveEvents(updatedEvents);
};
