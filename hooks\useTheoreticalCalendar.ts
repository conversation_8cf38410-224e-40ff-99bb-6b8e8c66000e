import { useState, useEffect, useCallback } from 'react';
import * as theoreticalCalendarService from '../services/theoreticalCalendarService';
import type { TheoreticalCalendar } from '../services/theoreticalCalendarService';

export const useTheoreticalCalendar = (year: number) => {
  const [calendar, setCalendar] = useState<TheoreticalCalendar>({});

  useEffect(() => {
    setCalendar(theoreticalCalendarService.getTheoreticalCalendar(year));
  }, [year]);
  
  const updateDayPlan = useCallback((date: string, shiftId: string | null) => {
    setCalendar(prevCalendar => {
      const newCalendar = { ...prevCalendar };
      if (shiftId) {
        newCalendar[date] = shiftId;
      } else {
        delete newCalendar[date];
      }
      theoreticalCalendarService.saveTheoreticalCalendar(year, newCalendar);
      return newCalendar;
    });
  }, [year]);

  const bulkUpdateMonth = useCallback((month: number, shiftId: string, days: string) => {
    setCalendar(prevCalendar => {
        const newCalendar = { ...prevCalendar };
        const daysInMonth = new Date(year, month + 1, 0).getDate();

        for (let day = 1; day <= daysInMonth; day++) {
            const date = new Date(Date.UTC(year, month, day));
            const dayOfWeek = date.getUTCDay(); // Sunday = 0, Monday = 1, ..., Saturday = 6
            const dateString = date.toISOString().split('T')[0];
            
            let shouldUpdate = false;
            if (days === 'all') {
                shouldUpdate = true;
            } else if (days === 'weekdays') {
                if (dayOfWeek >= 1 && dayOfWeek <= 5) {
                    shouldUpdate = true;
                }
            } else if (days === 'weekends') {
                if (dayOfWeek === 0 || dayOfWeek === 6) {
                    shouldUpdate = true;
                }
            } else {
                // Check for specific day of the week (e.g., '1' for Monday)
                if (String(dayOfWeek) === days) {
                    shouldUpdate = true;
                }
            }

            if (shouldUpdate) {
                newCalendar[dateString] = shiftId;
            }
        }
        theoreticalCalendarService.saveTheoreticalCalendar(year, newCalendar);
        return newCalendar;
    });
  }, [year]);

  return { calendar, updateDayPlan, bulkUpdateMonth };
};