import { useState, useEffect, useCallback } from 'react';
import * as theoreticalCalendarService from '../services/theoreticalCalendarService';
import type { TheoreticalCalendar } from '../services/theoreticalCalendarService';

export const useTheoreticalCalendar = (year: number) => {
  const [calendar, setCalendar] = useState<TheoreticalCalendar>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadCalendar = async () => {
      try {
        setLoading(true);
        setError(null);
        const loadedCalendar = await theoreticalCalendarService.getTheoreticalCalendar(year);
        setCalendar(loadedCalendar);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Error loading calendar');
        console.error('Error loading theoretical calendar:', err);
      } finally {
        setLoading(false);
      }
    };

    loadCalendar();
  }, [year]);

  const updateDayPlan = useCallback(async (date: string, shiftId: string | null) => {
    try {
      const newCalendar = { ...calendar };
      if (shiftId) {
        newCalendar[date] = shiftId;
      } else {
        delete newCalendar[date];
      }

      setCalendar(newCalendar);
      await theoreticalCalendarService.saveTheoreticalCalendar(year, newCalendar);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error updating calendar';
      setError(errorMessage);
      // Revert the optimistic update
      const revertedCalendar = await theoreticalCalendarService.getTheoreticalCalendar(year);
      setCalendar(revertedCalendar);
      throw err;
    }
  }, [year, calendar]);

  const bulkUpdateMonth = useCallback(async (month: number, shiftId: string, days: string) => {
    try {
      const newCalendar = { ...calendar };
      const daysInMonth = new Date(year, month + 1, 0).getDate();

      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(Date.UTC(year, month, day));
        const dayOfWeek = date.getUTCDay(); // Sunday = 0, Monday = 1, ..., Saturday = 6
        const dateString = date.toISOString().split('T')[0];

        let shouldUpdate = false;
        if (days === 'all') {
          shouldUpdate = true;
        } else if (days === 'weekdays') {
          if (dayOfWeek >= 1 && dayOfWeek <= 5) {
            shouldUpdate = true;
          }
        } else if (days === 'weekends') {
          if (dayOfWeek === 0 || dayOfWeek === 6) {
            shouldUpdate = true;
          }
        } else {
          // Check for specific day of the week (e.g., '1' for Monday)
          if (String(dayOfWeek) === days) {
            shouldUpdate = true;
          }
        }

        if (shouldUpdate) {
          newCalendar[dateString] = shiftId;
        }
      }

      setCalendar(newCalendar);
      await theoreticalCalendarService.saveTheoreticalCalendar(year, newCalendar);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error bulk updating calendar';
      setError(errorMessage);
      // Revert the optimistic update
      const revertedCalendar = await theoreticalCalendarService.getTheoreticalCalendar(year);
      setCalendar(revertedCalendar);
      throw err;
    }
  }, [year, calendar]);

  return { calendar, updateDayPlan, bulkUpdateMonth, loading, error };
};