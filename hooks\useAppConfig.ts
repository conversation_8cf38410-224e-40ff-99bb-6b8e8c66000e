import { useState, useEffect, useCallback } from 'react';
import type { EventTypeConfigItem, ShiftTypeConfigItem } from '../types';
import { DEFAULT_EVENT_TYPES, DEFAULT_SHIFT_TYPES } from '../constants';

const EVENT_TYPES_KEY = 'appConfig_eventTypes';
const SHIFT_TYPES_KEY = 'appConfig_shiftTypes';

const useStoredState = <T,>(key: string, defaultValue: T) => {
  const [state, setState] = useState<T>(() => {
    try {
      const storedValue = localStorage.getItem(key);
      return storedValue ? JSON.parse(storedValue) : defaultValue;
    } catch (error) {
      console.error(`Error reading from localStorage for key "${key}":`, error);
      return defaultValue;
    }
  });

  useEffect(() => {
    try {
      localStorage.setItem(key, JSON.stringify(state));
    } catch (error) {
      console.error(`Error writing to localStorage for key "${key}":`, error);
    }
  }, [key, state]);

  return [state, setState] as const;
};

export const useAppConfig = () => {
  const [eventTypes, setEventTypes] = useStoredState<EventTypeConfigItem[]>(EVENT_TYPES_KEY, DEFAULT_EVENT_TYPES);
  const [shiftTypes, setShiftTypes] = useStoredState<ShiftTypeConfigItem[]>(SHIFT_TYPES_KEY, DEFAULT_SHIFT_TYPES);

  // Event Type Handlers
  const addEventType = useCallback((item: Omit<EventTypeConfigItem, 'id' | 'isDeletable'>) => {
    setEventTypes(prev => [...prev, { ...item, id: crypto.randomUUID(), isDeletable: true }]);
  }, [setEventTypes]);

  const updateEventType = useCallback((updatedItem: EventTypeConfigItem) => {
    setEventTypes(prev => prev.map(item => item.id === updatedItem.id ? updatedItem : item));
  }, [setEventTypes]);

  const deleteEventType = useCallback((id: string) => {
    // Note: A robust implementation would check if the type is in use.
    setEventTypes(prev => prev.filter(item => item.id !== id));
  }, [setEventTypes]);


  // Shift Type Handlers
  const addShiftType = useCallback((item: Omit<ShiftTypeConfigItem, 'id' | 'isDeletable'>) => {
    setShiftTypes(prev => [...prev, { ...item, id: crypto.randomUUID(), isDeletable: true }]);
  }, [setShiftTypes]);

  const updateShiftType = useCallback((updatedItem: ShiftTypeConfigItem) => {
    setShiftTypes(prev => prev.map(item => item.id === updatedItem.id ? updatedItem : item));
  }, [setShiftTypes]);

  const deleteShiftType = useCallback((id: string) => {
    // Note: A robust implementation would check if the shift is in use.
    setShiftTypes(prev => prev.filter(item => item.id !== id));
  }, [setShiftTypes]);

  return {
    eventTypes,
    addEventType,
    updateEventType,
    deleteEventType,
    shiftTypes,
    addShiftType,
    updateShiftType,
    deleteShiftType,
  };
};
