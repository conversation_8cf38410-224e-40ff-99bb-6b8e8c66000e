// API Service for communicating with the backend
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

// Token management
const TOKEN_KEY = 'auth_token';

export const getAuthToken = (): string | null => {
  return localStorage.getItem(TOKEN_KEY);
};

export const setAuthToken = (token: string): void => {
  localStorage.setItem(TOKEN_KEY, token);
};

export const removeAuthToken = (): void => {
  localStorage.removeItem(TOKEN_KEY);
};

// API request wrapper with authentication
async function apiRequest<T>(
  endpoint: string, 
  options: RequestInit = {}
): Promise<T> {
  const token = getAuthToken();
  
  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  };

  const response = await fetch(`${API_BASE_URL}${endpoint}`, config);

  if (!response.ok) {
    if (response.status === 401) {
      // Token expired or invalid
      removeAuthToken();
      window.location.href = '/login'; // Redirect to login
      throw new Error('Authentication required');
    }
    
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json();
}

// API methods
export const api = {
  // GET request
  get: <T>(endpoint: string): Promise<T> => 
    apiRequest<T>(endpoint, { method: 'GET' }),

  // POST request
  post: <T>(endpoint: string, data?: any): Promise<T> => 
    apiRequest<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    }),

  // PUT request
  put: <T>(endpoint: string, data?: any): Promise<T> => 
    apiRequest<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    }),

  // DELETE request
  delete: <T>(endpoint: string): Promise<T> => 
    apiRequest<T>(endpoint, { method: 'DELETE' }),
};

// Authentication API
export const authApi = {
  login: async (username: string, password: string) => {
    const response = await api.post<{
      message: string;
      user: { id: number; username: string; email: string; fullName: string | null };
      token: string;
    }>('/auth/login', { username, password });
    
    setAuthToken(response.token);
    return response;
  },

  register: async (userData: {
    username: string;
    email: string;
    password: string;
    fullName?: string;
  }) => {
    const response = await api.post<{
      message: string;
      user: { id: number; username: string; email: string; fullName: string | null };
      token: string;
    }>('/auth/register', userData);
    
    setAuthToken(response.token);
    return response;
  },

  logout: async () => {
    try {
      await api.post('/auth/logout');
    } finally {
      removeAuthToken();
    }
  },

  getCurrentUser: () => 
    api.get<{ user: { id: number; username: string; email: string; fullName: string | null } }>('/auth/me'),
};

// Events API
export const eventsApi = {
  getAll: (filters?: {
    year?: number;
    month?: number;
    startDate?: string;
    endDate?: string;
  }) => {
    const params = new URLSearchParams();
    if (filters?.year) params.append('year', filters.year.toString());
    if (filters?.month) params.append('month', filters.month.toString());
    if (filters?.startDate) params.append('startDate', filters.startDate);
    if (filters?.endDate) params.append('endDate', filters.endDate);
    
    const queryString = params.toString();
    return api.get<any[]>(`/events${queryString ? `?${queryString}` : ''}`);
  },

  getById: (id: string) => api.get<any>(`/events/${id}`),

  create: (eventData: any) => api.post<any>('/events', eventData),

  update: (id: string, eventData: any) => api.put<any>(`/events/${id}`, eventData),

  delete: (id: string) => api.delete<{ message: string }>(`/events/${id}`),
};

// Event Types API
export const eventTypesApi = {
  getAll: () => api.get<any[]>('/event-types'),

  getById: (id: string) => api.get<any>(`/event-types/${id}`),

  create: (eventTypeData: any) => api.post<any>('/event-types', eventTypeData),

  update: (id: string, eventTypeData: any) => api.put<any>(`/event-types/${id}`, eventTypeData),

  delete: (id: string) => api.delete<{ message: string }>(`/event-types/${id}`),
};

// Shift Types API
export const shiftTypesApi = {
  getAll: () => api.get<any[]>('/shift-types'),

  getById: (id: string) => api.get<any>(`/shift-types/${id}`),

  create: (shiftTypeData: any) => api.post<any>('/shift-types', shiftTypeData),

  update: (id: string, shiftTypeData: any) => api.put<any>(`/shift-types/${id}`, shiftTypeData),

  delete: (id: string) => api.delete<{ message: string }>(`/shift-types/${id}`),
};

// Theoretical Calendar API
export const theoreticalCalendarApi = {
  getYear: (year: number) => api.get<Record<string, string>>(`/theoretical-calendar/${year}`),

  get: (filters?: {
    year?: number;
    month?: number;
    startDate?: string;
    endDate?: string;
  }) => {
    const params = new URLSearchParams();
    if (filters?.year) params.append('year', filters.year.toString());
    if (filters?.month) params.append('month', filters.month.toString());
    if (filters?.startDate) params.append('startDate', filters.startDate);
    if (filters?.endDate) params.append('endDate', filters.endDate);
    
    const queryString = params.toString();
    return api.get<Record<string, string>>(`/theoretical-calendar${queryString ? `?${queryString}` : ''}`);
  },

  setDay: (date: string, shiftId: string) => 
    api.put<{ message: string; date: string; shiftId: string }>('/theoretical-calendar/day', { date, shiftId }),

  removeDay: (date: string) => 
    api.delete<{ message: string; date: string }>(`/theoretical-calendar/day/${date}`),

  bulkUpdate: (entries: Array<{ date: string; shiftId?: string }>) => 
    api.put<{ message: string; entriesProcessed: number }>('/theoretical-calendar/bulk', { entries }),

  clearYear: (year: number) => 
    api.delete<{ message: string; entriesDeleted: number }>(`/theoretical-calendar/year/${year}`),
};

// Migration API
export const migrationApi = {
  fromLocalStorage: (data: {
    events?: any[];
    eventTypes?: any[];
    shiftTypes?: any[];
    theoreticalCalendars?: Record<string, Record<string, string>>;
  }) => api.post<any>('/migration/from-localstorage', data),

  export: () => api.get<any>('/migration/export'),
};

export default api;
