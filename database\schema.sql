-- Control de Jornada Laboral - Database Schema
-- SQLite compatible schema

-- Tabla de usuarios
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VA<PERSON><PERSON><PERSON>(100),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de tipos de eventos (EventTypeConfigItem)
CREATE TABLE IF NOT EXISTS event_types (
    id VARCHAR(50) PRIMARY KEY,
    user_id INTEGER NOT NULL,
    label VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL, -- 'Trabajo', 'Vacaciones', 'Permiso', 'Ausencia', 'Formación'
    computation_type VARCHAR(20) NOT NULL, -- 'adds', 'subtracts', 'neutral'
    color VARCHAR(20) NOT NULL,
    default_start_time VARCHAR(5), -- HH:mm format
    default_end_time VARCHAR(5), -- HH:mm format
    break_time VARCHAR(5) NOT NULL DEFAULT '00:00', -- HH:mm format
    default_shift_id VARCHAR(50),
    is_deletable BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (default_shift_id) REFERENCES shift_types(id) ON DELETE SET NULL
);

-- Tabla de tipos de turnos (ShiftTypeConfigItem)
CREATE TABLE IF NOT EXISTS shift_types (
    id VARCHAR(50) PRIMARY KEY,
    user_id INTEGER NOT NULL,
    label VARCHAR(100) NOT NULL,
    icon VARCHAR(10) NOT NULL,
    time_range VARCHAR(11) NOT NULL, -- 'HH:mm-HH:mm' format
    hours DECIMAL(4,2) NOT NULL,
    break_time VARCHAR(5) NOT NULL DEFAULT '00:00', -- HH:mm format
    is_deletable BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tabla de eventos (Event)
CREATE TABLE IF NOT EXISTS events (
    id VARCHAR(36) PRIMARY KEY, -- UUID
    user_id INTEGER NOT NULL,
    date DATE NOT NULL, -- YYYY-MM-DD format
    type_id VARCHAR(50) NOT NULL,
    title VARCHAR(200),
    shift_id VARCHAR(50),
    start_time VARCHAR(5), -- HH:mm format
    end_time VARCHAR(5), -- HH:mm format
    description TEXT,
    break_time VARCHAR(5), -- HH:mm format, allows override
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (type_id) REFERENCES event_types(id) ON DELETE RESTRICT,
    FOREIGN KEY (shift_id) REFERENCES shift_types(id) ON DELETE SET NULL
);

-- Tabla de calendario teórico (TheoreticalCalendar)
CREATE TABLE IF NOT EXISTS theoretical_calendar (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    date DATE NOT NULL, -- YYYY-MM-DD format
    shift_id VARCHAR(50) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (shift_id) REFERENCES shift_types(id) ON DELETE CASCADE,
    UNIQUE(user_id, date) -- Un usuario solo puede tener un turno teórico por día
);

-- Índices para mejorar rendimiento
CREATE INDEX IF NOT EXISTS idx_events_user_date ON events(user_id, date);
CREATE INDEX IF NOT EXISTS idx_events_type ON events(type_id);
CREATE INDEX IF NOT EXISTS idx_theoretical_calendar_user_date ON theoretical_calendar(user_id, date);
CREATE INDEX IF NOT EXISTS idx_event_types_user ON event_types(user_id);
CREATE INDEX IF NOT EXISTS idx_shift_types_user ON shift_types(user_id);

-- Triggers para actualizar updated_at automáticamente
CREATE TRIGGER IF NOT EXISTS update_users_updated_at 
    AFTER UPDATE ON users
    BEGIN
        UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_event_types_updated_at 
    AFTER UPDATE ON event_types
    BEGIN
        UPDATE event_types SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_shift_types_updated_at 
    AFTER UPDATE ON shift_types
    BEGIN
        UPDATE shift_types SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_events_updated_at 
    AFTER UPDATE ON events
    BEGIN
        UPDATE events SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_theoretical_calendar_updated_at 
    AFTER UPDATE ON theoretical_calendar
    BEGIN
        UPDATE theoretical_calendar SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

-- Insertar datos por defecto para tipos de eventos (se insertarán por usuario)
-- Estos se manejarán desde el backend cuando se cree un nuevo usuario

-- Insertar datos por defecto para tipos de turnos (se insertarán por usuario)
-- Estos se manejarán desde el backend cuando se cree un nuevo usuario
