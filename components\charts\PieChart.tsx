import React from 'react';

interface PieChartProps {
  title: string;
  data: { label: string; value: number; color: string }[];
}

const PALETTE: { [key: string]: string } = {
  'blue-500': '#3b82f6', 'green-500': '#22c55e', 'purple-500': '#a855f7',
  'yellow-500': '#eab308', 'red-500': '#ef4444', 'gray-500': '#6b7280',
  'pink-500': '#ec4899', 'indigo-500': '#6366f1', 'teal-500': '#14b8a6',
  'orange-500': '#f97316', 'lime-500': '#84cc16', 'cyan-500': '#06b6d4',
  default: '#9ca3af'
};

const PieChart: React.FC<PieChartProps> = ({ title, data }) => {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  if (total === 0) {
    return (
        <div>
            <h3 className="text-lg font-bold text-gray-700 mb-4 text-center">{title}</h3>
            <div className="flex items-center justify-center h-48 text-gray-500">No hay datos para mostrar</div>
        </div>
    );
  }

  let cumulativePercent = 0;

  const segments = data.map(item => {
    const percent = (item.value / total) * 100;
    const startAngle = (cumulativePercent / 100) * 360;
    cumulativePercent += percent;
    const endAngle = (cumulativePercent / 100) * 360;
    return { ...item, percent, startAngle, endAngle };
  });

  const getCoordinatesForPercent = (percent: number) => {
    const x = Math.cos(2 * Math.PI * percent);
    const y = Math.sin(2 * Math.PI * percent);
    return [x, y];
  };

  return (
    <div>
      <h3 className="text-lg font-bold text-gray-700 mb-4 text-center">{title}</h3>
      <div className="flex flex-col md:flex-row items-center justify-center space-x-0 md:space-x-4">
        <div className="relative w-40 h-40">
          <svg viewBox="-1.2 -1.2 2.4 2.4" className="transform -rotate-90">
             {segments.map((segment, index) => {
                const [startX, startY] = getCoordinatesForPercent(segment.startAngle / 360);
                const [endX, endY] = getCoordinatesForPercent(segment.endAngle / 360);
                const largeArcFlag = segment.percent > 50 ? 1 : 0;
                const pathData = `M ${startX} ${startY} A 1 1 0 ${largeArcFlag} 1 ${endX} ${endY}`;
                
                return (
                     <path
                        key={index}
                        d={pathData}
                        stroke={PALETTE[segment.color] || PALETTE.default}
                        strokeWidth="0.4"
                        fill="none"
                        className="transition-all duration-300"
                    />
                );
             })}
          </svg>
           <div className="absolute inset-0 flex items-center justify-center text-center">
                <span className="text-2xl font-bold text-gray-700">{total}</span>
                <span className="text-sm text-gray-500 ml-1">días</span>
           </div>
        </div>
        <ul className="mt-4 md:mt-0 text-sm space-y-1 text-gray-600">
            {segments.map((item, index) => (
                <li key={index} className="flex items-center">
                    <span className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: PALETTE[item.color] || PALETTE.default }}></span>
                    <span>{item.label}:</span>
                    <span className="font-semibold ml-1">{item.value} ({item.percent.toFixed(1)}%)</span>
                </li>
            ))}
        </ul>
      </div>
    </div>
  );
};

export default PieChart;
