import React from 'react';

interface DonutChartProps {
  title: string;
  data: { label: string; value: number; color: string; hours?: number }[];
  centerText?: string;
  centerSubtext?: string;
  showHours?: boolean;
}

const PALETTE: { [key: string]: string } = {
  'blue-500': '#3b82f6', 'green-500': '#22c55e', 'purple-500': '#a855f7',
  'yellow-500': '#eab308', 'red-500': '#ef4444', 'gray-500': '#6b7280',
  'pink-500': '#ec4899', 'indigo-500': '#6366f1', 'teal-500': '#14b8a6',
  'orange-500': '#f97316', 'lime-500': '#84cc16', 'cyan-500': '#06b6d4',
  default: '#9ca3af'
};

const DonutChart: React.FC<DonutChartProps> = ({ 
  title, 
  data, 
  centerText, 
  centerSubtext, 
  showHours = false 
}) => {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  const totalHours = showHours ? data.reduce((sum, item) => sum + (item.hours || 0), 0) : 0;
  
  if (total === 0) {
    return (
      <div>
        <h3 className="text-lg font-bold text-gray-700 mb-4 text-center">{title}</h3>
        <div className="flex items-center justify-center h-48 text-gray-500">
          No hay datos para mostrar
        </div>
      </div>
    );
  }

  let cumulativePercent = 0;
  const segments = data.map(item => {
    const percent = (item.value / total) * 100;
    const startAngle = (cumulativePercent / 100) * 360;
    cumulativePercent += percent;
    const endAngle = (cumulativePercent / 100) * 360;
    return { ...item, percent, startAngle, endAngle };
  });

  const getCoordinatesForPercent = (percent: number) => {
    const x = Math.cos(2 * Math.PI * percent);
    const y = Math.sin(2 * Math.PI * percent);
    return [x, y];
  };

  return (
    <div>
      <h3 className="text-lg font-bold text-gray-700 mb-4 text-center">{title}</h3>
      <div className="flex flex-col md:flex-row items-center justify-center space-x-0 md:space-x-6">
        {/* Donut Chart */}
        <div className="relative w-48 h-48">
          <svg viewBox="-1.2 -1.2 2.4 2.4" className="transform -rotate-90">
            {segments.map((segment, index) => {
              const [startX, startY] = getCoordinatesForPercent(segment.startAngle / 360);
              const [endX, endY] = getCoordinatesForPercent(segment.endAngle / 360);
              const largeArcFlag = segment.percent > 50 ? 1 : 0;
              
              // Outer arc
              const outerPathData = `M ${startX} ${startY} A 1 1 0 ${largeArcFlag} 1 ${endX} ${endY}`;
              
              // Inner arc (for donut hole)
              const innerRadius = 0.6;
              const [innerStartX, innerStartY] = [startX * innerRadius, startY * innerRadius];
              const [innerEndX, innerEndY] = [endX * innerRadius, endY * innerRadius];
              const innerPathData = `M ${innerEndX} ${innerEndY} A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${innerStartX} ${innerStartY}`;
              
              const fullPath = `${outerPathData} L ${innerEndX} ${innerEndY} ${innerPathData} Z`;

              return (
                <path
                  key={index}
                  d={fullPath}
                  fill={PALETTE[segment.color] || PALETTE.default}
                  className="hover:opacity-80 transition-opacity duration-200"
                />
              );
            })}
          </svg>
          
          {/* Center Content */}
          <div className="absolute inset-0 flex flex-col items-center justify-center text-center">
            <span className="text-2xl font-bold text-gray-700">
              {centerText || total}
            </span>
            <span className="text-sm text-gray-500">
              {centerSubtext || (showHours ? `${totalHours.toFixed(1)}h` : 'eventos')}
            </span>
          </div>
        </div>
        
        {/* Legend */}
        <div className="mt-4 md:mt-0 text-sm space-y-2 text-gray-600 max-w-xs">
          {segments.map((item, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center flex-1 min-w-0">
                <span
                  className="w-3 h-3 rounded-full mr-3 flex-shrink-0"
                  style={{ backgroundColor: PALETTE[item.color] || PALETTE.default }}
                ></span>
                <span className="truncate">{item.label}</span>
              </div>
              <div className="ml-2 text-right flex-shrink-0">
                <div className="font-semibold">
                  {item.value} ({item.percent.toFixed(1)}%)
                </div>
                {showHours && item.hours !== undefined && (
                  <div className="text-xs text-gray-400">
                    {item.hours.toFixed(1)}h
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DonutChart;
