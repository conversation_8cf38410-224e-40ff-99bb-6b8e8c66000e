import express from 'express';
import { body, validationResult, param } from 'express-validator';
import { db } from '../database/db.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// Get all shift types for the authenticated user
router.get('/', async (req, res) => {
  try {
    const shiftTypes = await db('shift_types')
      .where('user_id', req.user.id)
      .select(
        'id',
        'label',
        'icon',
        'time_range as time',
        'hours',
        'break_time as breakTime',
        'is_deletable as isDeletable',
        'created_at as createdAt',
        'updated_at as updatedAt'
      )
      .orderBy('label');

    res.json(shiftTypes);

  } catch (error) {
    console.error('Get shift types error:', error);
    res.status(500).json({ error: 'Failed to fetch shift types' });
  }
});

// Get single shift type
router.get('/:id', [
  param('id').isLength({ min: 1, max: 50 }).trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const shiftType = await db('shift_types')
      .where('id', req.params.id)
      .where('user_id', req.user.id)
      .select(
        'id',
        'label',
        'icon',
        'time_range as time',
        'hours',
        'break_time as breakTime',
        'is_deletable as isDeletable',
        'created_at as createdAt',
        'updated_at as updatedAt'
      )
      .first();

    if (!shiftType) {
      return res.status(404).json({ error: 'Shift type not found' });
    }

    res.json(shiftType);

  } catch (error) {
    console.error('Get shift type error:', error);
    res.status(500).json({ error: 'Failed to fetch shift type' });
  }
});

// Create new shift type
router.post('/', [
  body('id').isLength({ min: 1, max: 50 }).trim(),
  body('label').isLength({ min: 1, max: 100 }).trim(),
  body('icon').isLength({ min: 1, max: 10 }).trim(),
  body('time').matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]-([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  body('hours').isFloat({ min: 0, max: 24 }),
  body('breakTime').matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  body('isDeletable').isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id, label, icon, time, hours, breakTime, isDeletable } = req.body;

    // Check if shift type ID already exists for this user
    const existingShiftType = await db('shift_types')
      .where('id', id)
      .where('user_id', req.user.id)
      .first();

    if (existingShiftType) {
      return res.status(409).json({ error: 'Shift type with this ID already exists' });
    }

    const shiftTypeData = {
      id,
      user_id: req.user.id,
      label,
      icon,
      time_range: time,
      hours,
      break_time: breakTime,
      is_deletable: isDeletable
    };

    await db('shift_types').insert(shiftTypeData);

    // Return the created shift type
    const createdShiftType = await db('shift_types')
      .where('id', id)
      .where('user_id', req.user.id)
      .select(
        'id',
        'label',
        'icon',
        'time_range as time',
        'hours',
        'break_time as breakTime',
        'is_deletable as isDeletable',
        'created_at as createdAt',
        'updated_at as updatedAt'
      )
      .first();

    res.status(201).json(createdShiftType);

  } catch (error) {
    console.error('Create shift type error:', error);
    res.status(500).json({ error: 'Failed to create shift type' });
  }
});

// Update shift type
router.put('/:id', [
  param('id').isLength({ min: 1, max: 50 }).trim(),
  body('label').isLength({ min: 1, max: 100 }).trim(),
  body('icon').isLength({ min: 1, max: 10 }).trim(),
  body('time').matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]-([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  body('hours').isFloat({ min: 0, max: 24 }),
  body('breakTime').matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  body('isDeletable').isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { label, icon, time, hours, breakTime, isDeletable } = req.body;

    // Verify shift type exists and belongs to user
    const existingShiftType = await db('shift_types')
      .where('id', req.params.id)
      .where('user_id', req.user.id)
      .first();

    if (!existingShiftType) {
      return res.status(404).json({ error: 'Shift type not found' });
    }

    const updateData = {
      label,
      icon,
      time_range: time,
      hours,
      break_time: breakTime,
      is_deletable: isDeletable
    };

    await db('shift_types')
      .where('id', req.params.id)
      .where('user_id', req.user.id)
      .update(updateData);

    // Return the updated shift type
    const updatedShiftType = await db('shift_types')
      .where('id', req.params.id)
      .where('user_id', req.user.id)
      .select(
        'id',
        'label',
        'icon',
        'time_range as time',
        'hours',
        'break_time as breakTime',
        'is_deletable as isDeletable',
        'created_at as createdAt',
        'updated_at as updatedAt'
      )
      .first();

    res.json(updatedShiftType);

  } catch (error) {
    console.error('Update shift type error:', error);
    res.status(500).json({ error: 'Failed to update shift type' });
  }
});

// Delete shift type
router.delete('/:id', [
  param('id').isLength({ min: 1, max: 50 }).trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    // Verify shift type exists and belongs to user
    const existingShiftType = await db('shift_types')
      .where('id', req.params.id)
      .where('user_id', req.user.id)
      .first();

    if (!existingShiftType) {
      return res.status(404).json({ error: 'Shift type not found' });
    }

    // Check if shift type is deletable
    if (!existingShiftType.is_deletable) {
      return res.status(400).json({ error: 'This shift type cannot be deleted' });
    }

    // Check if shift type is in use by events
    const eventsUsingShift = await db('events')
      .where('shift_id', req.params.id)
      .where('user_id', req.user.id)
      .count('id as count')
      .first();

    // Check if shift type is in use by theoretical calendar
    const calendarUsingShift = await db('theoretical_calendar')
      .where('shift_id', req.params.id)
      .where('user_id', req.user.id)
      .count('id as count')
      .first();

    // Check if shift type is referenced by event types
    const eventTypesUsingShift = await db('event_types')
      .where('default_shift_id', req.params.id)
      .where('user_id', req.user.id)
      .count('id as count')
      .first();

    const totalUsage = eventsUsingShift.count + calendarUsingShift.count + eventTypesUsingShift.count;

    if (totalUsage > 0) {
      return res.status(400).json({ 
        error: 'Cannot delete shift type that is in use',
        usage: {
          events: eventsUsingShift.count,
          calendar: calendarUsingShift.count,
          eventTypes: eventTypesUsingShift.count
        }
      });
    }

    const deletedCount = await db('shift_types')
      .where('id', req.params.id)
      .where('user_id', req.user.id)
      .del();

    if (deletedCount === 0) {
      return res.status(404).json({ error: 'Shift type not found' });
    }

    res.json({ message: 'Shift type deleted successfully' });

  } catch (error) {
    console.error('Delete shift type error:', error);
    res.status(500).json({ error: 'Failed to delete shift type' });
  }
});

export default router;
