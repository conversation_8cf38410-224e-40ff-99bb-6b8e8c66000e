import express from 'express';
import { body, validationResult, param } from 'express-validator';
import { db } from '../database/db.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// Get all event types for the authenticated user
router.get('/', async (req, res) => {
  try {
    const eventTypes = await db('event_types')
      .where('user_id', req.user.id)
      .select(
        'id',
        'label',
        'category',
        'computation_type as computationType',
        'color',
        'default_start_time as defaultStartTime',
        'default_end_time as defaultEndTime',
        'break_time as breakTime',
        'default_shift_id as defaultShiftId',
        'is_deletable as isDeletable',
        'created_at as createdAt',
        'updated_at as updatedAt'
      )
      .orderBy('label');

    res.json(eventTypes);

  } catch (error) {
    console.error('Get event types error:', error);
    res.status(500).json({ error: 'Failed to fetch event types' });
  }
});

// Get single event type
router.get('/:id', [
  param('id').isLength({ min: 1, max: 50 }).trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const eventType = await db('event_types')
      .where('id', req.params.id)
      .where('user_id', req.user.id)
      .select(
        'id',
        'label',
        'category',
        'computation_type as computationType',
        'color',
        'default_start_time as defaultStartTime',
        'default_end_time as defaultEndTime',
        'break_time as breakTime',
        'default_shift_id as defaultShiftId',
        'is_deletable as isDeletable',
        'created_at as createdAt',
        'updated_at as updatedAt'
      )
      .first();

    if (!eventType) {
      return res.status(404).json({ error: 'Event type not found' });
    }

    res.json(eventType);

  } catch (error) {
    console.error('Get event type error:', error);
    res.status(500).json({ error: 'Failed to fetch event type' });
  }
});

// Create new event type
router.post('/', [
  body('id').isLength({ min: 1, max: 50 }).trim(),
  body('label').isLength({ min: 1, max: 100 }).trim(),
  body('category').isIn(['Trabajo', 'Vacaciones', 'Permiso', 'Ausencia', 'Formación']),
  body('computationType').isIn(['adds', 'subtracts', 'neutral']),
  body('color').isLength({ min: 1, max: 20 }).trim(),
  body('defaultStartTime').optional().matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  body('defaultEndTime').optional().matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  body('breakTime').matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  body('defaultShiftId').optional().isLength({ max: 50 }).trim(),
  body('isDeletable').isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const {
      id,
      label,
      category,
      computationType,
      color,
      defaultStartTime,
      defaultEndTime,
      breakTime,
      defaultShiftId,
      isDeletable
    } = req.body;

    // Check if event type ID already exists for this user
    const existingEventType = await db('event_types')
      .where('id', id)
      .where('user_id', req.user.id)
      .first();

    if (existingEventType) {
      return res.status(409).json({ error: 'Event type with this ID already exists' });
    }

    // Verify default shift exists if provided
    if (defaultShiftId) {
      const shiftType = await db('shift_types')
        .where('id', defaultShiftId)
        .where('user_id', req.user.id)
        .first();

      if (!shiftType) {
        return res.status(400).json({ error: 'Invalid default shift type' });
      }
    }

    const eventTypeData = {
      id,
      user_id: req.user.id,
      label,
      category,
      computation_type: computationType,
      color,
      default_start_time: defaultStartTime || null,
      default_end_time: defaultEndTime || null,
      break_time: breakTime,
      default_shift_id: defaultShiftId || null,
      is_deletable: isDeletable
    };

    await db('event_types').insert(eventTypeData);

    // Return the created event type
    const createdEventType = await db('event_types')
      .where('id', id)
      .where('user_id', req.user.id)
      .select(
        'id',
        'label',
        'category',
        'computation_type as computationType',
        'color',
        'default_start_time as defaultStartTime',
        'default_end_time as defaultEndTime',
        'break_time as breakTime',
        'default_shift_id as defaultShiftId',
        'is_deletable as isDeletable',
        'created_at as createdAt',
        'updated_at as updatedAt'
      )
      .first();

    res.status(201).json(createdEventType);

  } catch (error) {
    console.error('Create event type error:', error);
    res.status(500).json({ error: 'Failed to create event type' });
  }
});

// Update event type
router.put('/:id', [
  param('id').isLength({ min: 1, max: 50 }).trim(),
  body('label').isLength({ min: 1, max: 100 }).trim(),
  body('category').isIn(['Trabajo', 'Vacaciones', 'Permiso', 'Ausencia', 'Formación']),
  body('computationType').isIn(['adds', 'subtracts', 'neutral']),
  body('color').isLength({ min: 1, max: 20 }).trim(),
  body('defaultStartTime').optional().matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  body('defaultEndTime').optional().matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  body('breakTime').matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  body('defaultShiftId').optional().isLength({ max: 50 }).trim(),
  body('isDeletable').isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const {
      label,
      category,
      computationType,
      color,
      defaultStartTime,
      defaultEndTime,
      breakTime,
      defaultShiftId,
      isDeletable
    } = req.body;

    // Verify event type exists and belongs to user
    const existingEventType = await db('event_types')
      .where('id', req.params.id)
      .where('user_id', req.user.id)
      .first();

    if (!existingEventType) {
      return res.status(404).json({ error: 'Event type not found' });
    }

    // Verify default shift exists if provided
    if (defaultShiftId) {
      const shiftType = await db('shift_types')
        .where('id', defaultShiftId)
        .where('user_id', req.user.id)
        .first();

      if (!shiftType) {
        return res.status(400).json({ error: 'Invalid default shift type' });
      }
    }

    const updateData = {
      label,
      category,
      computation_type: computationType,
      color,
      default_start_time: defaultStartTime || null,
      default_end_time: defaultEndTime || null,
      break_time: breakTime,
      default_shift_id: defaultShiftId || null,
      is_deletable: isDeletable
    };

    await db('event_types')
      .where('id', req.params.id)
      .where('user_id', req.user.id)
      .update(updateData);

    // Return the updated event type
    const updatedEventType = await db('event_types')
      .where('id', req.params.id)
      .where('user_id', req.user.id)
      .select(
        'id',
        'label',
        'category',
        'computation_type as computationType',
        'color',
        'default_start_time as defaultStartTime',
        'default_end_time as defaultEndTime',
        'break_time as breakTime',
        'default_shift_id as defaultShiftId',
        'is_deletable as isDeletable',
        'created_at as createdAt',
        'updated_at as updatedAt'
      )
      .first();

    res.json(updatedEventType);

  } catch (error) {
    console.error('Update event type error:', error);
    res.status(500).json({ error: 'Failed to update event type' });
  }
});

// Delete event type
router.delete('/:id', [
  param('id').isLength({ min: 1, max: 50 }).trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    // Verify event type exists and belongs to user
    const existingEventType = await db('event_types')
      .where('id', req.params.id)
      .where('user_id', req.user.id)
      .first();

    if (!existingEventType) {
      return res.status(404).json({ error: 'Event type not found' });
    }

    // Check if event type is deletable
    if (!existingEventType.is_deletable) {
      return res.status(400).json({ error: 'This event type cannot be deleted' });
    }

    // Check if event type is in use
    const eventsUsingType = await db('events')
      .where('type_id', req.params.id)
      .where('user_id', req.user.id)
      .count('id as count')
      .first();

    if (eventsUsingType.count > 0) {
      return res.status(400).json({ 
        error: 'Cannot delete event type that is in use by events',
        eventsCount: eventsUsingType.count
      });
    }

    const deletedCount = await db('event_types')
      .where('id', req.params.id)
      .where('user_id', req.user.id)
      .del();

    if (deletedCount === 0) {
      return res.status(404).json({ error: 'Event type not found' });
    }

    res.json({ message: 'Event type deleted successfully' });

  } catch (error) {
    console.error('Delete event type error:', error);
    res.status(500).json({ error: 'Failed to delete event type' });
  }
});

export default router;
