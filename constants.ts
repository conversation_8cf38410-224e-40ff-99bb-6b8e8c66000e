
import { EventTypeConfigItem, ShiftTypeConfigItem, EventCategory, ComputationType } from './types';

export const MONTH_NAMES = [
  '<PERSON><PERSON>', 'Febrero', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>',
  '<PERSON>', 'A<PERSON><PERSON>', 'Septiembre', 'Octubre', 'Noviem<PERSON>', 'Diciembre'
];

export const DAY_NAMES_SHORT = ['Lun', 'Mar', 'Mi<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>áb', 'Dom'];

export const EVENT_CATEGORIES = Object.values(EventCategory);
export const COMPUTATION_TYPES = [
  { value: ComputationType.Adds, label: 'Suma horas' },
  { value: ComputationType.Subtracts, label: 'Resta horas' },
  { value: ComputationType.Neutral, label: 'Neutro' },
];


export const DEFAULT_EVENT_TYPES: EventTypeConfigItem[] = [
  { id: 'jornada_laboral', label: 'Jornada Laboral', color: 'blue-500', isDeletable: false, category: EventCategory.Trabajo, computationType: ComputationType.Adds, breakTime: '00:15' },
  { id: 'vacaciones', label: 'Vacaciones', color: 'green-500', isDeletable: false, category: EventCategory.Vacaciones, computationType: ComputationType.Neutral, breakTime: '00:00' },
  { id: 'formacion', label: 'Formación', color: 'purple-500', isDeletable: false, category: EventCategory.Formacion, computationType: ComputationType.Adds, breakTime: '00:00', defaultStartTime: '09:00', defaultEndTime: '13:00' },
  { id: 'permiso', label: 'Permiso', color: 'yellow-500', isDeletable: false, category: EventCategory.Permiso, computationType: ComputationType.Neutral, breakTime: '00:00' },
  { id: 'baja_medica', label: 'Baja Médica', color: 'red-500', isDeletable: false, category: EventCategory.Ausencia, computationType: ComputationType.Neutral, breakTime: '00:00' },
  { id: 'asunto_propio', label: 'Asunto Propio', color: 'gray-500', isDeletable: false, category: EventCategory.Permiso, computationType: ComputationType.Subtracts, breakTime: '00:00' },
  { id: 'horas_compensadas', label: 'Horas Compensadas', color: 'cyan-500', isDeletable: false, category: EventCategory.Permiso, computationType: ComputationType.Subtracts, breakTime: '00:00' },
];

export const DEFAULT_SHIFT_TYPES: ShiftTypeConfigItem[] = [
  { id: 'manana', label: 'Mañana', icon: '☀️', time: '06:00-14:00', hours: 7.75, isDeletable: false, breakTime: '00:15' },
  { id: 'tarde', label: 'Tarde', icon: '🌙', time: '14:00-22:00', hours: 7.75, isDeletable: false, breakTime: '00:15' },
  { id: 'noche', label: 'Noche', icon: '🦉', time: '22:00-06:00', hours: 7.75, isDeletable: false, breakTime: '00:15' },
  { id: 'festivo_manana', label: 'Festivo Mañana', icon: '🎉', time: '06:00-18:00', hours: 11, isDeletable: false, breakTime: '01:00' },
  { id: 'festivo_noche', label: 'Festivo Noche', icon: '🎆', time: '18:00-06:00', hours: 11, isDeletable: false, breakTime: '01:00' },
];
