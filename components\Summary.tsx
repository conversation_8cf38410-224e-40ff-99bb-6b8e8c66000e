import React, { useMemo } from 'react';
import { Event, EventTypeConfigItem, ShiftTypeConfigItem, calculateEventNetHours } from '../types';
import { MONTH_NAMES } from '../constants';
import type { TheoreticalCalendar } from '../services/theoreticalCalendarService';

interface SummaryProps {
  events: Event[];
  month: number;
  year: number;
  theoreticalCalendar: TheoreticalCalendar;
  eventTypesMap: Record<string, EventTypeConfigItem>;
  shiftTypesMap: Record<string, ShiftTypeConfigItem>;
}

const Summary: React.FC<SummaryProps> = ({ events, month, year, theoreticalCalendar, eventTypesMap, shiftTypesMap }) => {
  
  const workedHours = useMemo(() => {
    return events.reduce((total, event) => {
      const eventType = eventTypesMap[event.typeId];
      if (!eventType) return total;
      return total + calculateEventNetHours(event, eventType);
    }, 0);
  }, [events, eventTypesMap]);

  const theoreticalHours = useMemo(() => {
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    let total = 0;
    for (let day = 1; day <= daysInMonth; day++) {
        const dateString = new Date(Date.UTC(year, month, day)).toISOString().split('T')[0];
        const shiftId = theoreticalCalendar[dateString];
        if (shiftId && shiftTypesMap[shiftId]) {
            total += shiftTypesMap[shiftId].hours;
        }
    }
    return total;
  }, [month, year, theoreticalCalendar, shiftTypesMap]);

  const balance = workedHours - theoreticalHours;

  const eventCounts = events.reduce((acc, event) => {
    const typeId = event.typeId;
    acc[typeId] = (acc[typeId] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
      <h3 className="text-xl font-bold text-gray-700 mb-4">Resumen de {MONTH_NAMES[month]} {year}</h3>
      <div className="space-y-4">
        <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex justify-between items-center text-gray-600">
                <span>Horas Teóricas:</span>
                <span className="font-bold text-gray-800">{theoreticalHours.toFixed(2)}h</span>
            </div>
            <div className="flex justify-between items-center text-gray-600 mt-1">
                <span>Horas Computadas:</span>
                <span className="font-bold text-gray-800">{workedHours.toFixed(2)}h</span>
            </div>
            <div className="flex justify-between items-center mt-2 pt-2 border-t">
                <span className="font-semibold">Balance:</span>
                <span className={`font-bold text-lg ${balance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {balance >= 0 ? '+' : ''}{balance.toFixed(2)}h
                </span>
            </div>
        </div>
        <div>
            <h4 className="font-semibold text-gray-600 mb-2">Desglose de eventos:</h4>
            <ul className="text-sm space-y-1 text-gray-500">
                {Object.entries(eventCounts).map(([typeId, count]) => (
                    <li key={typeId} className="flex justify-between">
                        <span>{eventTypesMap[typeId]?.label || typeId}:</span>
                        <span>{count} {count > 1 ? 'días' : 'día'}</span>
                    </li>
                ))}
            </ul>
        </div>
      </div>
    </div>
  );
};

export default Summary;