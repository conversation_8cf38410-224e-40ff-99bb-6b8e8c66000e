import React, { useState, useMemo } from 'react';
import type { useAppConfig } from '../hooks/useAppConfig';
import { EventTypeConfigItem, ShiftTypeConfigItem, EventCategory, ComputationType } from '../types';
import { EVENT_CATEGORIES, COMPUTATION_TYPES } from '../constants';

type Config = ReturnType<typeof useAppConfig>;

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  config: Config;
}

const parseTimeToMinutes = (time: string): number => {
  if (!time || !time.includes(':')) return 0;
  const [hours, minutes] = time.split(':').map(Number);
  if (isNaN(hours) || isNaN(minutes)) return 0;
  return hours * 60 + minutes;
};

const calculateNetHours = (schedule: string, breakTime: string): number => {
  if (!schedule || !schedule.includes('-')) return 0;
  const [startStr, endStr] = schedule.split('-');
  let startMinutes = parseTimeToMinutes(startStr);
  let endMinutes = parseTimeToMinutes(endStr);
  const breakMinutes = parseTimeToMinutes(breakTime);
  if (endMinutes < startMinutes) { // Overnight shift
    endMinutes += 24 * 60;
  }
  const durationMinutes = endMinutes - startMinutes;
  const netMinutes = durationMinutes - breakMinutes;
  return netMinutes > 0 ? netMinutes / 60 : 0;
};


const SettingsModal: React.FC<SettingsModalProps> = ({ isOpen, onClose, config }) => {
  const [editingEventType, setEditingEventType] = useState<Partial<EventTypeConfigItem> | null>(null);
  const [editingShiftType, setEditingShiftType] = useState<Partial<ShiftTypeConfigItem> | null>(null);

  if (!isOpen) return null;
  
  const handleSaveEventType = (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      if (!editingEventType?.label || !editingEventType?.color || !editingEventType.category || !editingEventType.computationType) return;

      const payload = {
        label: editingEventType.label,
        color: editingEventType.color,
        category: editingEventType.category,
        computationType: editingEventType.computationType,
        breakTime: editingEventType.breakTime || '00:00',
        defaultShiftId: editingEventType.defaultShiftId || undefined,
        defaultStartTime: editingEventType.defaultStartTime || undefined,
        defaultEndTime: editingEventType.defaultEndTime || undefined,
      };
      
      if (editingEventType.id) {
          config.updateEventType({ ...payload, id: editingEventType.id, isDeletable: editingEventType.isDeletable ?? false});
      } else {
          config.addEventType(payload);
      }
      setEditingEventType(null);
  }
  
  const handleSaveShiftType = (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      if (!editingShiftType?.label || !editingShiftType?.icon || !editingShiftType?.time) return;
      
      const hours = calculateNetHours(editingShiftType.time, editingShiftType.breakTime || '00:00');

      const payload = {
        label: editingShiftType.label,
        icon: editingShiftType.icon,
        time: editingShiftType.time,
        breakTime: editingShiftType.breakTime || '00:00',
        hours,
      }
      
      if (editingShiftType.id) {
          config.updateShiftType({ ...payload, id: editingShiftType.id, isDeletable: editingShiftType.isDeletable ?? false });
      } else {
          config.addShiftType(payload);
      }
      setEditingShiftType(null);
  }
  
  const computedHours = editingShiftType?.time ? calculateNetHours(editingShiftType.time, editingShiftType.breakTime || '00:00').toFixed(2) : '0.00';

  const defaultEventType: Partial<EventTypeConfigItem> = {
      label: '',
      category: EventCategory.Trabajo,
      computationType: ComputationType.Adds,
      color: 'blue-500',
      breakTime: '00:00',
      defaultShiftId: '',
      defaultStartTime: '',
      defaultEndTime: ''
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 z-50 flex justify-center items-center p-4">
      <div className="bg-gray-100 rounded-xl shadow-2xl w-full max-w-4xl p-6 relative animate-fade-in-up flex flex-col" style={{ height: '95vh' }}>
        <div className="flex-shrink-0 flex justify-between items-center mb-6 pb-4 border-b border-gray-300">
          <h2 className="text-3xl font-bold text-gray-800">Ajustes</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-800">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
          </button>
        </div>

        <div className="overflow-y-auto flex-grow pr-3 grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Event Types Section */}
          <div className="space-y-4">
            <h3 className="text-xl font-bold text-gray-700">Tipos de Evento</h3>
            <div className="bg-white p-4 rounded-lg shadow space-y-3">
              {config.eventTypes.map(item => (
                <div key={item.id} className="flex items-center justify-between p-2 rounded-md hover:bg-gray-50">
                  <div className="flex items-center">
                    <span className={`w-4 h-4 rounded-full mr-3 bg-${item.color}`}></span>
                    <div>
                        <span className="font-medium">{item.label}</span>
                        <span className="text-xs text-gray-500 ml-2">({item.category} / {item.computationType})</span>
                    </div>
                  </div>
                  <div className="space-x-2">
                    <button onClick={() => setEditingEventType(item)} className="text-sm text-indigo-600 hover:underline">Editar</button>
                    {item.isDeletable && <button onClick={() => confirm('¿Seguro?') && config.deleteEventType(item.id)} className="text-sm text-red-600 hover:underline">Eliminar</button>}
                  </div>
                </div>
              ))}
            </div>
            {editingEventType ? (
                 <form onSubmit={handleSaveEventType} className="bg-white p-4 rounded-lg shadow space-y-4 border-2 border-indigo-500">
                    <h4 className="font-semibold text-lg">{editingEventType.id ? 'Editar' : 'Añadir'} Tipo de Evento</h4>
                    <div>
                        <label className="text-sm font-medium text-gray-700">Nombre</label>
                        <input type="text" placeholder="Nombre del evento" value={editingEventType.label || ''} onChange={e => setEditingEventType(p => ({...p, label: e.target.value}))} required className="mt-1 w-full border border-gray-300 rounded px-3 py-2"/>
                    </div>
                     <div className="grid grid-cols-2 gap-4">
                        <div>
                           <label className="text-sm font-medium text-gray-700">Categoría</label>
                           <select value={editingEventType.category || ''} onChange={e => setEditingEventType(p => ({...p, category: e.target.value as EventCategory}))} required className="mt-1 w-full border border-gray-300 rounded px-3 py-2">
                            {EVENT_CATEGORIES.map(cat => <option key={cat} value={cat}>{cat}</option>)}
                           </select>
                        </div>
                        <div>
                           <label className="text-sm font-medium text-gray-700">Cómputo</label>
                           <select value={editingEventType.computationType || ''} onChange={e => setEditingEventType(p => ({...p, computationType: e.target.value as ComputationType}))} required className="mt-1 w-full border border-gray-300 rounded px-3 py-2">
                             {COMPUTATION_TYPES.map(comp => <option key={comp.value} value={comp.value}>{comp.label}</option>)}
                           </select>
                        </div>
                     </div>
                     <div>
                        <label className="text-sm font-medium text-gray-700">Turno por defecto (Opcional)</label>
                        <select value={editingEventType.defaultShiftId || ''} onChange={e => setEditingEventType(p => ({...p, defaultShiftId: e.target.value, defaultStartTime: '', defaultEndTime: ''}))} className="mt-1 w-full border border-gray-300 rounded px-3 py-2">
                            <option value="">Ninguno</option>
                            {config.shiftTypes.map(shift => <option key={shift.id} value={shift.id}>{shift.label}</option>)}
                        </select>
                     </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                            <label className="text-sm font-medium text-gray-700">Hora Inicio (defecto)</label>
                            <input type="time" disabled={!!editingEventType.defaultShiftId} value={editingEventType.defaultStartTime || ''} onChange={e => setEditingEventType(p => ({...p, defaultStartTime: e.target.value}))} className="mt-1 w-full border border-gray-300 rounded px-3 py-2 disabled:bg-gray-100"/>
                        </div>
                        <div>
                            <label className="text-sm font-medium text-gray-700">Hora Fin (defecto)</label>
                            <input type="time" disabled={!!editingEventType.defaultShiftId} value={editingEventType.defaultEndTime || ''} onChange={e => setEditingEventType(p => ({...p, defaultEndTime: e.target.value}))} className="mt-1 w-full border border-gray-300 rounded px-3 py-2 disabled:bg-gray-100"/>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                           <label className="text-sm font-medium text-gray-700">Descanso (HH:mm)</label>
                           <input type="text" placeholder="00:00" value={editingEventType.breakTime || ''} onChange={e => setEditingEventType(p => ({...p, breakTime: e.target.value}))} pattern="[0-9]{2}:[0-9]{2}" className="mt-1 w-full border border-gray-300 rounded px-3 py-2"/>
                        </div>
                         <div>
                            <label className="text-sm font-medium text-gray-700">Color</label>
                            <input type="text" placeholder="e.g., green-500" value={editingEventType.color || ''} onChange={e => setEditingEventType(p => ({...p, color: e.target.value}))} required className="mt-1 w-full border border-gray-300 rounded px-3 py-2"/>
                         </div>
                      </div>
                     <div className="flex justify-end space-x-2 pt-2"><button type="button" onClick={() => setEditingEventType(null)} className="px-4 py-2 bg-gray-200 rounded-md">Cancelar</button><button type="submit" className="px-4 py-2 bg-indigo-600 text-white rounded-md">Guardar</button></div>
                </form>
            ) : <button onClick={() => setEditingEventType(defaultEventType)} className="w-full py-2 bg-indigo-600 text-white font-bold rounded-lg hover:bg-indigo-700">Añadir Tipo de Evento</button>}
          </div>

          {/* Shift Types Section */}
          <div className="space-y-4">
            <h3 className="text-xl font-bold text-gray-700">Tipos de Turno</h3>
            <div className="bg-white p-4 rounded-lg shadow space-y-3">
              {config.shiftTypes.map(item => (
                <div key={item.id} className="flex items-center justify-between p-2 rounded-md hover:bg-gray-50">
                    <div><span className="mr-2">{item.icon}</span><span className="font-medium">{item.label}</span><span className="text-sm text-gray-500 ml-2">({item.time} / {item.hours.toFixed(2)}h)</span></div>
                    <div className="space-x-2">
                        <button onClick={() => setEditingShiftType(item)} className="text-sm text-indigo-600 hover:underline">Editar</button>
                        {item.isDeletable && <button onClick={() => confirm('¿Seguro?') && config.deleteShiftType(item.id)} className="text-sm text-red-600 hover:underline">Eliminar</button>}
                    </div>
                </div>
              ))}
            </div>
             {editingShiftType ? (
                 <form onSubmit={handleSaveShiftType} className="bg-white p-4 rounded-lg shadow space-y-3 border-2 border-indigo-500">
                    <h4 className="font-semibold text-lg">{editingShiftType.id ? 'Editar' : 'Añadir'} Tipo de Turno</h4>
                     <input type="text" placeholder="Nombre del turno" value={editingShiftType.label || ''} onChange={e => setEditingShiftType(p => ({...p, label: e.target.value}))} required className="w-full border border-gray-300 rounded px-3 py-2"/>
                     <div className="grid grid-cols-2 gap-4">
                        <input type="text" placeholder="Icono (e.g., ☀️)" value={editingShiftType.icon || ''} onChange={e => setEditingShiftType(p => ({...p, icon: e.target.value}))} required className="border border-gray-300 rounded px-3 py-2"/>
                        <input type="text" placeholder="Horario (HH:mm-HH:mm)" pattern="[0-9]{2}:[0-9]{2}-[0-9]{2}:[0-9]{2}" value={editingShiftType.time || ''} onChange={e => setEditingShiftType(p => ({...p, time: e.target.value}))} required className="border border-gray-300 rounded px-3 py-2"/>
                     </div>
                     <input type="text" placeholder="Descanso (HH:mm)" pattern="[0-9]{2}:[0-9]{2}" value={editingShiftType.breakTime || ''} onChange={e => setEditingShiftType(p => ({...p, breakTime: e.target.value}))} className="w-full border border-gray-300 rounded px-3 py-2"/>
                     <p className="text-sm text-gray-600 bg-gray-100 p-2 rounded-md">Horas computadas: <span className="font-bold">{computedHours}h</span></p>
                     <div className="flex justify-end space-x-2"><button type="button" onClick={() => setEditingShiftType(null)} className="px-4 py-2 bg-gray-200 rounded-md">Cancelar</button><button type="submit" className="px-4 py-2 bg-indigo-600 text-white rounded-md">Guardar</button></div>
                </form>
            ) : <button onClick={() => setEditingShiftType({})} className="w-full py-2 bg-indigo-600 text-white font-bold rounded-lg hover:bg-indigo-700">Añadir Tipo de Turno</button>}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsModal;