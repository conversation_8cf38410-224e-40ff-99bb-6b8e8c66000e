import React from 'react';

interface LineChartDataset {
  label: string;
  data: number[];
  color: string;
  strokeColor: string;
}

interface LineChartProps {
  title: string;
  data: {
    labels: string[];
    datasets: LineChartDataset[];
  };
  yAxisLabel?: string;
}

const PALETTE: { [key: string]: string } = {
  'blue-500': '#3b82f6',
  'green-500': '#22c55e',
  'purple-500': '#a855f7',
  'yellow-500': '#eab308',
  'red-500': '#ef4444',
  'pink-500': '#ec4899',
  'indigo-500': '#6366f1',
  'teal-500': '#14b8a6',
  'orange-500': '#f97316',
  'gray-500': '#6b7280',
  default: '#9ca3af'
};

const LineChart: React.FC<LineChartProps> = ({ title, data, yAxisLabel = 'Horas' }) => {
  const maxValue = Math.max(...data.datasets.flatMap(ds => ds.data));
  const minValue = Math.min(...data.datasets.flatMap(ds => ds.data));
  const yAxisMax = Math.ceil(maxValue / 10) * 10 || 10;
  const yAxisMin = Math.floor(minValue / 10) * 10;
  const yAxisRange = yAxisMax - yAxisMin;

  const getYPosition = (value: number) => {
    return ((yAxisMax - value) / yAxisRange) * 100;
  };

  const getXPosition = (index: number) => {
    return (index / (data.labels.length - 1)) * 100;
  };

  return (
    <div className="h-full flex flex-col">
      <h3 className="text-lg font-bold text-gray-700 mb-2 text-center">{title}</h3>
      <div className="flex-grow flex flex-col">
        {/* Chart Container */}
        <div className="flex-grow flex relative">
          {/* Y Axis Labels */}
          <div className="w-12 text-right pr-2 text-xs text-gray-500 flex flex-col justify-between">
            <span>{yAxisMax}{yAxisLabel === 'Horas' ? 'h' : ''}</span>
            <span>{Math.round((yAxisMax + yAxisMin) / 2)}{yAxisLabel === 'Horas' ? 'h' : ''}</span>
            <span>{yAxisMin}{yAxisLabel === 'Horas' ? 'h' : ''}</span>
          </div>
          
          {/* Chart Area */}
          <div className="flex-grow border-l border-b border-gray-200 relative" style={{ height: '200px' }}>
            {/* Grid Lines */}
            <div className="absolute inset-0">
              <div className="h-1/2 border-b border-dashed border-gray-200"></div>
              {data.labels.map((_, index) => (
                <div
                  key={index}
                  className="absolute top-0 h-full border-l border-dashed border-gray-100"
                  style={{ left: `${getXPosition(index)}%` }}
                ></div>
              ))}
            </div>
            
            {/* SVG for Lines and Points */}
            <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
              {data.datasets.map((dataset, datasetIndex) => {
                const points = dataset.data.map((value, index) => ({
                  x: getXPosition(index),
                  y: getYPosition(value)
                }));
                
                const pathData = points.reduce((path, point, index) => {
                  return path + (index === 0 ? `M ${point.x} ${point.y}` : ` L ${point.x} ${point.y}`);
                }, '');

                return (
                  <g key={datasetIndex}>
                    {/* Line */}
                    <path
                      d={pathData}
                      fill="none"
                      stroke={PALETTE[dataset.strokeColor] || PALETTE.default}
                      strokeWidth="0.5"
                      vectorEffect="non-scaling-stroke"
                    />
                    {/* Points */}
                    {points.map((point, pointIndex) => (
                      <circle
                        key={pointIndex}
                        cx={point.x}
                        cy={point.y}
                        r="1"
                        fill={PALETTE[dataset.color] || PALETTE.default}
                        vectorEffect="non-scaling-stroke"
                        className="hover:r-2 transition-all duration-200"
                      />
                    ))}
                  </g>
                );
              })}
            </svg>
            
            {/* Hover tooltips */}
            {data.labels.map((label, index) => (
              <div
                key={index}
                className="absolute top-0 h-full w-1 group cursor-pointer"
                style={{ left: `${getXPosition(index)}%` }}
              >
                <div className="absolute -top-16 left-1/2 -translate-x-1/2 hidden group-hover:block bg-gray-800 text-white text-xs rounded py-2 px-3 pointer-events-none whitespace-nowrap z-10">
                  <div className="font-semibold">{label}</div>
                  {data.datasets.map((dataset, dsIndex) => (
                    <div key={dsIndex} className="flex items-center mt-1">
                      <span
                        className="w-2 h-2 rounded-full mr-2"
                        style={{ backgroundColor: PALETTE[dataset.color] || PALETTE.default }}
                      ></span>
                      <span>{dataset.label}: {dataset.data[index].toFixed(2)}{yAxisLabel === 'Horas' ? 'h' : ''}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* X Axis Labels */}
        <div className="w-full flex pl-12">
          <div className="flex-grow flex justify-between text-center text-xs text-gray-500 pt-2">
            {data.labels.map((label, index) => (
              <span key={index} className="transform -rotate-45 origin-center">
                {label.length > 6 ? label.substring(0, 6) + '...' : label}
              </span>
            ))}
          </div>
        </div>
        
        {/* Legend */}
        <div className="flex justify-center flex-wrap gap-4 mt-4 text-sm text-gray-600">
          {data.datasets.map((dataset, index) => (
            <div key={index} className="flex items-center">
              <span
                className="w-3 h-3 rounded-full mr-2"
                style={{ backgroundColor: PALETTE[dataset.color] || PALETTE.default }}
              ></span>
              <span>{dataset.label}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LineChart;
